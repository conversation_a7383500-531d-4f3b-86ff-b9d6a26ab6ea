package org.thanos.platform.fw.auth.dao;


import org.thanos.platform.fw.auth.damain.SysUserGroup;
import org.thanos.platform.fw.auth.damain.SysUserGroupWithRole;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public interface SysUserGroupDao {

	/**
	 * 根据用户ID查询该用户所在组。
	 * 
	 * @param userId
	 * @return
	 */
	public List<SysUserGroup> getSysUserGroupByUserId(String userId);

	/**
	 * 根据用户ID清空该用户组信息。
	 * 
	 * @param userId
	 * @return
	 */
	public int delSysUserGroupByUserId(String userId);

	/**
	 * 追加该用户组信息。
	 * 
	 * @param userId
	 * @return
	 */
	public int addSysUserGroup(SysUserGroup sysUserGroup);

	/**
	 * 根据用户组ID查询该组所有用户信息。
	 * 
	 * @param groupId
	 * @return
	 */
	public List<SysUserGroup> getSysUserGroupByGroupId(String groupId);

	List<SysUserGroupWithRole> getSysUserGroupAndRoleByUserId(String userId);

	/**
	 * 根据用户ID组清空该用户信息。
	 * 
	 * @param groupId
	 * @return
	 */
	public int delSysUserGroupByGroupId(String groupId);

}
