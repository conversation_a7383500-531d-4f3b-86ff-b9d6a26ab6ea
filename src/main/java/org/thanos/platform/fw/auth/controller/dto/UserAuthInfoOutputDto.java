package org.thanos.platform.fw.auth.controller.dto;

import jp.co.nttdata.sz.web.controller.iosuser.dto.StoreDto;

import org.thanos.platform.fw.auth.damain.SysRoleInfoOutput;

import java.io.Serializable;
import java.util.List;

public class UserAuthInfoOutputDto implements Serializable{

	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = -5487117505003796668L;

	/**
	 * 用户名
	 */
	private String userName;

	private String userAvatar;
	/**
	 * 用户名
	 */
	private String userId;
	/**
	 * 分配端末id
	 */
	private String clientId;

	/**
	 * 签名
	 */
	private String sign;

	private String accessToken;

	private String createTime;

	private String phoneNumber;

	private String mail;

	private List<StoreDto> stores;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	private List<SysRoleInfoOutput> roles;

	public List<SysRoleInfoOutput> getRoles() {
		return roles;
	}

	public void setRoles(List<SysRoleInfoOutput> roles) {
		this.roles = roles;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public String getMail() {
		return mail;
	}

	public void setMail(String mail) {
		this.mail = mail;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getUserAvatar() {
		return userAvatar;
	}

	public void setUserAvatar(String userAvatar) {
		this.userAvatar = userAvatar;
	}

	public List<StoreDto> getStores() {
		return stores;
	}

	public void setStores(List<StoreDto> stores) {
		this.stores = stores;
	}
}
