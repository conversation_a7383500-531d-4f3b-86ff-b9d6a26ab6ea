package org.thanos.platform.fw.auth.util;

import java.math.BigDecimal;
import java.util.List;

import jp.co.nttdata.sz.web.entity.OrderItem;

public class MathUtil {
	
	/**
	 * 计算订单明细的总数量和总金额
	 * @param orderItems
	 * @return
	 */
	public static Object[] countOrderItem(List<OrderItem> orderItems) {
		//初始数量和金额
		int countQuantity =0;
	    BigDecimal countAmount = new BigDecimal(0.00);
	    countAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
	    //迭代明细
		for(OrderItem item:orderItems) {
			//订单商品数量增加
			countQuantity+=item.getQuantity();
//			BigDecimal price = item.getPromPrice().equals(BigDecimal.ZERO)?item.getPrice():item.getPromPrice();
			//运算订单总金额
			countAmount=new BigDecimal(item.getQuantity())
					.multiply((item.getPromPrice().compareTo(new BigDecimal("0")) > 0 ? item.getPromPrice() : item.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP))
					.add(countAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
		}
		//返回值设定
		Object[] result = {countQuantity,countAmount};
		return result;
		
	}

}
