/*
 * システム名：Aquarea
 * 業務名：メッセージ情報の操作用のユーティリティ
 * ファイル名：OcclusionService.java
 * 著作権表記：
 * Copyright (C) 2019 NCIT CORPORATION
 * 作成履歴
 * 20190902/NCIT/新規作成
 */
package org.thanos.platform.fw.auth.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thanos.platform.fw.auth.damain.SysOcclusion;
import org.thanos.platform.fw.auth.dao.SysOcclusionDao;
import org.thanos.platform.fw.auth.service.SysOcclusionService;

import java.util.List;

/**
 * 闭塞服务
 *
 * <AUTHOR>
 */
@Service
public class SysOcclusionServiceImpl implements SysOcclusionService {

    @Autowired
    SysOcclusionDao sysOcclusionDao;

    @Override
    public SysOcclusion selectByApName(String apName) {
        SysOcclusion result = sysOcclusionDao.selectByApName(apName);
        return result;
    }

    @Override
    public List<SysOcclusion> selectApNames() {
        return sysOcclusionDao.selectApNames();
    }

}
