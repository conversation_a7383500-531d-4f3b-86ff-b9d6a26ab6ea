/*
 * システム名：Aquarea
 * 業務名：メッセージ情報の操作用のユーティリティ
 * ファイル名：SysUserDao.java
 * 著作権表記：
 * Copyright (C) 2019 NCIT CORPORATION
 * 作成履歴
 * 20190902/NCIT/新規作成
 */
package org.thanos.platform.fw.auth.dao;


import java.util.List;
import java.util.Map;

import jp.co.nttdata.sz.web.entity.MenuConfigEntity;
import org.apache.ibatis.annotations.Param;
import org.thanos.platform.fw.auth.damain.SysUser;

import jp.co.nttdata.sz.web.controller.user.dto.UserGroupInfoDto;
import jp.co.nttdata.sz.web.controller.user.dto.UserSearchInputDto;

/**
 * ユーザ情報
 *
 * <AUTHOR>
 */
public interface SysUserDao {

	/**
	 * 通过登陆用户名取得用户的信息.
	 *
	 * @param userAccount
	 * @return
	 */
	public SysUser getSysUserByUserAccount(String userAccount);

	/**
	 * 通过登陆用户名取得用户的信息.
	 *
	 * @param userAccount
	 * @return
	 */
	public SysUser getSysUserByUserAccountOnly(String userAccount);

	/**
	 * 通过登陆用户ID取得用户的信息.
	 *
	 * @param userId
	 * @return
	 */
	public SysUser getSysUserByUserId( String storeId);

	/**
	 * 查询ALL用户信息.
	 *
	 * @return
	 */
	public List<SysUser> getSysUserAll(Map<String,String> user);

	/**
	 * 追加用户信息.
	 *
	 * @param sysUser
	 * @return
	 */
	public int addSysUser(SysUser sysUser);

	public int addSysUserById(SysUser sysUser);

	/**
	 * 更新用户信息.
	 *
	 * @param user
	 * @return
	 */
	public int updateSysUserByUserId(SysUser user);

	/**
	 * 更新用户信息(修改密码).
	 *
	 * @param userId
	 * @return
	 */
	public int updateSysUserPwdByUserId(Map<String,String> paramMap);

	/**
	 * 删除用户信息（物理删除）.
	 *
	 * @param userId
	 * @return
	 */
	public int delSysUserByUserId(String userId);

	/**
	 * 删除用户信息（伦理删除：禁用）.
	 *
	 * @param userId
	 * @return
	 */
	public int delSysUserByUserId2(String userId);

	/**
	 * 检测相同登录账号的个数 .
	 * @param userAccount
	 * @return
	 */
	public int getCountByUserAccount(String userAccount);

	/**
	 * 检测同一个店铺下，相同登录账号的个数 .
	 * @param userAccount
	 * @param storeId
	 * @return
	 */
	public int getCountByStaffNumberAndStore(String staffNumber,String storeId);

	/**
	 * 手机号码重复性检测.
	 * @param map
	 * @return
	 */
	public int getCountByUserPhone(String userPhone);

	/**
	 * 域名重复性检测.
	 * @param map
	 * @return
	 */
	public int getCountByUserUrl(String userUrl);

	/**
	 * 用户信息检索
	 * @param inputDto
	 * @return
	 */
	public List<SysUser> searchUser(UserSearchInputDto inputDto);

	public List<UserGroupInfoDto> searchUserGroup(String  userId);

	public int delUserGroupByUserId(@Param("userId") String userId);

	public int addUserGroup(@Param("userId") String userId, @Param("groupId") String groupId);


	/**
	 * 手机端会员信息修改
	 * @param user 会员信息
	 * @return
	 */
	public int updateSysUserInfo(SysUser user);

	public int updateStoreOperationUser(SysUser user);

	public SysUser getUserByOpenId(String openId);

	public int getMaxUserId();


	public SysUser getUserByStaffNumber(String staffNumber);
	public List<MenuConfigEntity> getMenuConfig();
}
