package org.thanos.platform.fw.auth.dao;


import org.thanos.platform.fw.auth.damain.SysResource;

import jp.co.nttdata.sz.web.controller.resource.dto.SysResourceOutput;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface SysResourceDao {

	/**
	 * 
	 * @return
	 */
	public List<SysResourceOutput> getSysResourceAll();
	
	/**
	 * 查询资源所有信息 (可用only).
	 * 
	 * @return
	 */
	public List<SysResource> getSysResourceAll2();

	/**
	 * 
	 * @param res
	 * @return
	 */
	public int addSysResource(SysResource res);

	/**
	 * 
	 * @return
	 */
	public int delSysResourceByResId(Long resId);

	/**
	 * 
	 * @param res
	 * @return
	 */
	public int updSysResourceByResId(SysResource res);

	/**
	 * 
	 * @param res
	 * @return
	 */
	public int updSysResourceOrderByResId(Map<String, String> map);

	/**
	 * 
	 * @param map
	 * @return
	 */
	public List<SysResourceOutput> getSysResourceData(Long resId);
	
	
	/**
	 * 
	 * @param map
	 * @return
	 */
	public SysResource getMaxOrderId(Map<String, Object> map);
}
