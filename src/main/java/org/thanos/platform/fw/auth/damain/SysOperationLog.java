package org.thanos.platform.fw.auth.damain;

import java.time.LocalDateTime;

/**
 * 系统操作log
 * <AUTHOR>
 *
 */
public class SysOperationLog {
	
	/**
	 * 日志id
	 */
	private Integer id;
	
	/**
	 * 操作用户id
	 */
	private String userId;
	
	/**
	 * 操作 名
	 */
	private String operationName;
	
	/**
	 * 操作信息
	 */
	private String operationMessage;
	
	/**
	 * 操作时间
	 */
	private LocalDateTime  operationTime;

	public Integer getId() {
		return id;
	}

	public String  getUserId() {
		return userId;
	}

	public String getOperationName() {
		return operationName;
	}

	public String getOperationMessage() {
		return operationMessage;
	}

	public LocalDateTime getOperationTime() {
		return operationTime;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public void setOperationName(String operationName) {
		this.operationName = operationName;
	}

	public void setOperationMessage(String operationMessage) {
		this.operationMessage = operationMessage;
	}

	public void setOperationTime(LocalDateTime operationTime) {
		this.operationTime = operationTime;
	}
	
	

}
