package org.thanos.platform.fw.auth.service;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface SysAuthorityResourceService {

    /**
     * 通过url取得用户的权限一览信息.
     *
     * @param url
     * @return
     */
    List<String> getAuthoritiesByUrl(String url);

    /**
     * 根据权限ID查询该权限的资源信息。
     *
     * @param authorityId
     * @return
     */
    List<Map<String, Object>> getSysAuthorityResourceByAuthorityId(String authorityId);

    /**
     * 根据权限D更新该权限对应资源信息。
     *
     * @param authorityId
     * @param resources
     * @return
     */
    int updateSysAuthorityResourceByAuthorityId(String authorityId, String resources);

}
