package org.thanos.core.mqtt.util;
/**
 * Created by cong.ding on 2020/04/28.
 */
public class IosMessageData {
	
	/**
	 * 用户标识
	 */
    private String userId;
    
    /**
     * 消息id
     */
    private String messageId;

    private String messageType;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getMessageId() {
		return messageId;
	}

	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	/**
	 * 构造器 处理用户加入
	 * @param userId
	 * @return
	 */
    public IosMessageData buildUser(String userId) {
    	this.setUserId(userId);
		return this; 	
    }
    
    /**
           * 构造器 messageId加入
     * @param messageId
     * @return
     */
    public IosMessageData buildMessage(String messageId,String messageType) {
    	this.setMessageId(messageId);
    	this.setMessageType(messageType);
		return this; 	
    }

    
    /**
     * 加密操作
     * @param key
     * @return
     */
    public IosMessageData encrypt(String key) {
    	//TODOD 加密操作
		return this; 
    }
    
}
