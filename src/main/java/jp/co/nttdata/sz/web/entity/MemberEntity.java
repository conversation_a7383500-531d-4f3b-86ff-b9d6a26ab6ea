package jp.co.nttdata.sz.web.entity;

import java.math.BigDecimal;

/**
 * 会员信息entity
 * <AUTHOR>
 *
 */
public class MemberEntity {

	/**
	 * 	用户账号
	 */
	private String userAccount;

	/**
	 *	用户名
	 */
	private String userName;
	/**
	 *	用户名
	 */
	private boolean isFaceAuthed;

	/**
	 *	用户头像
	 */
	private String memberAvatar;

	private String userPhone;

	private String wxGroup;

	private BigDecimal amount;

	private BigDecimal discountAmount;

	private BigDecimal unpaidAmount;

	private String[] groupId;

	public String getUserAccount() {
		return userAccount;
	}

	public void setUserAccount(String userAccount) {
		this.userAccount = userAccount;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getMemberAvatar() {
		return memberAvatar;
	}

	public void setMemberAvatar(String avatar) {
		this.memberAvatar = avatar;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getWxGroup() {
		return wxGroup;
	}

	public void setWxGroup(String wxGroup) {
		this.wxGroup = wxGroup;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public BigDecimal getUnpaidAmount() {
		return unpaidAmount;
	}

	public void setUnpaidAmount(BigDecimal unpaidAmount) {
		this.unpaidAmount = unpaidAmount;
	}

	public String[] getGroupId() {
		return groupId;
	}

	public void setGroupId(String[] groupId) {
		this.groupId = groupId;
	}

	public boolean isFaceAuthed() {
		return isFaceAuthed;
	}

	public void setFaceAuthed(boolean faceAuthed) {
		isFaceAuthed = faceAuthed;
	}
}
