package jp.co.nttdata.sz.web.mapper;

import jp.co.nttdata.sz.web.entity.MstProductsCost;

import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface MstProductsCostMapper {
    /**
     * 查询货物
     *
     * @param mstProductsCost 货物
     * @return 货物
     */
    public MstProductsCost selectMstProductsCost(MstProductsCost mstProductsCost);

    /**
     * 查询货物列表
     *
     * @param mstProductsCost 货物
     * @return 货物集合
     */
    public List<MstProductsCost> selectMstProductsCostList(MstProductsCost mstProductsCost);

    /**
     * 新增货物
     *
     * @param mstProductsCost 货物
     * @return 结果
     */
    public int insertMstProductsCost(MstProductsCost mstProductsCost);


    /**
     * 修改货物
     *
     * @param mstProductsCost 货物
     * @return 结果
     */
    public int updateMstProductsCost(MstProductsCost mstProductsCost);


    /**
     * 删除货物
     *
     * @param mstProductsCost 货物
     * @return 结果
     */
    public int deleteMstProductsCost(MstProductsCost mstProductsCost);

    /**
     * 批量删除货物
     *
     * @param mstProductsCosts 需要删除的货物集合
     * @return 结果
     */
    public int batchDeleteMstProductsCost(@Param("mstProductsCosts") List<MstProductsCost> mstProductsCosts);

}
