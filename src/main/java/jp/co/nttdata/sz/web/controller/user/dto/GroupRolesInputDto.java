package jp.co.nttdata.sz.web.controller.user.dto;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * Created by cong.ding on 2020/04/21.
 */
public class GroupRolesInputDto  {
	
	@NotNull(message="groupId can not be null")
	@NotEmpty(message="groupId can not be empty")
	private String groupId;

    private List<String> roles;

	public String getGroupId() {
		return groupId;
	}

	public List<String> getRoles() {
		return roles;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public void setRoles(List<String> roles) {
		this.roles = roles;
	}

    
}
