package jp.co.nttdata.sz.web.service.salesreturn;

import java.util.List;

import jp.co.nttdata.sz.web.controller.iosorder.dto.IosOrderReturnInputDto;
import jp.co.nttdata.sz.web.controller.staff.dto.StaffOrderReturnInputDto;
import jp.co.nttdata.sz.web.entity.SalesReturnInfoEntity;
import jp.co.nttdata.sz.web.entity.SalesReturnItemEntity;
import jp.co.nttdata.sz.web.service.activiti.dto.ReturnSearchInputDto;
import jp.co.nttdata.sz.web.service.salesreturn.dto.ReturnInfoOutputDto;
import jp.co.nttdata.sz.web.service.salesreturn.dto.SalesReturnSearchInputDto;
import jp.co.nttdata.sz.web.service.salesreturn.dto.SalesReturnSearchOutputDto;

/**
 * 退货处理service
 * <AUTHOR>
 *
 */
public interface SalesReturnOperationService {
	
	/**
	 * 检索退货订单
	 * @param entity
	 * @return
	 */
	public List<SalesReturnSearchOutputDto> searchSalesReturn(SalesReturnSearchInputDto entity);
	
	public List<ReturnInfoOutputDto> searchSalesReturnByCondition(ReturnSearchInputDto entity);
	
	public List<SalesReturnSearchOutputDto> searchWaitDealSalesReturn(String storeId);
	
	/**
	 * 根据退货id数组取得退货信息
	 * @param returnIds
	 * @return
	 */
	public List<ReturnInfoOutputDto>  getSalesReturnInfoByIds(List<String> returnIds); 
	
	/**
	 * 
	 * @param returnIds
	 * @param o
	 * @return
	 */
	public List<ReturnInfoOutputDto>  getSalesReturnInfoByIds(List<String> returnIds,ReturnSearchInputDto o); 
	
	/**
	 * 执行退货(订单退货全退)
	 * @param returnId
	 * @return
	 */
	public int doSalesReturn(String returnId);
	
	public int refuseSalesReturn(String returnId);
	
	/**
	 * 执行退货(根据退货项目退货)
	 * @param returnId
	 * @return
	 */
	public int doSalesReturnByItem(List<SalesReturnItemEntity> items);
	
	/**
	 * 更新退货单状态
	 * @param entity
	 * @return
	 */
	public int updateSalesStatus(SalesReturnInfoEntity entity);
	
	/**
	 * 根据订单号创建退货
	 * @param orderId
	 * @return
	 */
	public int inertSalesReturn(IosOrderReturnInputDto inputDto,String returnId);


	/**
	 * 従業員端退货
	 * @param inputDto 输入信息
	 */
	public void staffReturnOrder(StaffOrderReturnInputDto inputDto);
}
