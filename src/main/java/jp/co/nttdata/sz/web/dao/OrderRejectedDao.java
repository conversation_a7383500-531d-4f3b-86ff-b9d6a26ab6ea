package jp.co.nttdata.sz.web.dao;

import jp.co.nttdata.sz.web.entity.OrderRejected;

import java.util.List;

public interface OrderRejectedDao {

    /**
     * 查询订单驳回表
     * @param orderId
     * @return
     */
    List<OrderRejected> getOrderRejectedByOrderId(String orderId);

    /**
     * 插入订单驳回表
     * @param orderRejected
     * @return
     */
    public int insertOrderRejected(OrderRejected orderRejected);

}
