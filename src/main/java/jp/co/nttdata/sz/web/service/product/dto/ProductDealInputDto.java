package jp.co.nttdata.sz.web.service.product.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductDealInputDto {

    /**
     *  商品名称
     */
    private String title;

    /**
     * 分类
     */
    private String category;

    /**
     * 销售价格
     */
    private BigDecimal price;

    /**
     * 销量
     */
    private Integer salesVolume;

    /**
     * 重量
     */
    private int weight;

    /**
     * 商品编号
     */
    private String itemId;

    /**
     * Barcode
     */
    private String barcode;

    /**
     * 图片url
     */
    private String picUrl;

    /**
     * 库存
     */
    private int stock;

    /**
     * 店铺id
     */
    private String storeId;

    /**
     * 单位id
     */
    private Long organizationUnitId;

    /**
     * multiple_mst_products_category表主键id
     */
    private Integer id;
}
