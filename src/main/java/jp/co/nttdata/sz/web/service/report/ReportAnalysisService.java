package jp.co.nttdata.sz.web.service.report;

import jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto;

import java.util.List;
import java.util.Map;

public interface ReportAnalysisService {

    public List<Map> searchProductEliminate(ReportAnalysisInputDto inputDto);

    public List<Map> searchProductGrossProfit(ReportAnalysisInputDto inputDto);

    public List<Map> searchProductOrderAmount(ReportAnalysisInputDto inputDto);

    public List<Map> searchProductOrderNumber(ReportAnalysisInputDto inputDto);

    public List<Map> searchProductAbc(ReportAnalysisInputDto inputDto);

    public List<Map> searchProductTurnoverRate(ReportAnalysisInputDto inputDto);

    public List<Map> searchProductAdviceTurnoverRate(ReportAnalysisInputDto inputDto);

    public List<Map> searchProductCategoryTurnoverRate(ReportAnalysisInputDto inputDto);

    public List<Map> searchProductAdviceCategoryTurnoverRate(ReportAnalysisInputDto inputDto);

    public List<Map> searchProductShopTurnoverRate(ReportAnalysisInputDto inputDto);

    public List<Map> searchProductAdviceShopTurnoverRate(ReportAnalysisInputDto inputDto);

    public List<Map> searchMemberOverview(ReportAnalysisInputDto inputDto);

    public List<Map> searchMemberRfm(ReportAnalysisInputDto inputDto);

    public List<Map> searchMemberPurchaseFrequency(ReportAnalysisInputDto inputDto);

    public List<Map> searchMemberSex(ReportAnalysisInputDto inputDto);

    public List<Map> searchMemberPricePreference(ReportAnalysisInputDto inputDto);

    public List<Map> searchMemberActivityTime(ReportAnalysisInputDto inputDto);

    public List<Map> searchMemberActivity(ReportAnalysisInputDto inputDto);
}
