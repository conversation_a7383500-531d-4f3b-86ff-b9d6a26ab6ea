package jp.co.nttdata.sz.web.controller.iosuser;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jp.co.nttdata.sz.web.controller.dictionary.dto.SysDictionaryInputDto;
import jp.co.nttdata.sz.web.controller.iosuser.dto.*;
import jp.co.nttdata.sz.web.controller.user.dto.CustomerSearchInputDto;
import jp.co.nttdata.sz.web.entity.MemberEntity;
import jp.co.nttdata.sz.web.entity.SysGlobalConfigEntity;
import jp.co.nttdata.sz.web.service.dictionary.SysDictionaryService;
import jp.co.nttdata.sz.web.service.iosuser.IosUserService;
import jp.co.nttdata.sz.web.service.iosuser.dto.UserRegitserInputDto;
import jp.co.nttdata.sz.web.service.sysGlobalConfig.GlobalConfigCode;
import jp.co.nttdata.sz.web.service.sysGlobalConfig.SysGlobalConfigService;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.thanos.core.message.LocaleMessageSourceService;
import org.thanos.core.message.MessageCode;
import org.thanos.platform.fw.auth.damain.SysCustomer;
import org.thanos.platform.fw.auth.service.SysUserService;
import org.thanos.platform.fw.constants.CodeConstant;
import org.thanos.platform.fw.filter.MDCFilter;
import org.thanos.platform.fw.model.ClientResponseJsonBean;
import org.thanos.platform.fw.model.ServerRequestJsonBean;
import org.thanos.platform.util.ResultDataUtil;
import org.thanos.platform.util.StringUtil;
import org.thanos.platform.util.VaildErrorUtil;
import org.thymeleaf.util.StringUtils;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 客户信息管理用Controller
 * <AUTHOR>
 *
 */

@Api(value = "客户信息controller")
@RequestMapping("ios")
@RestController
public class IosCustomerController {

	/**
	 * msg
	 */
	@Autowired
	private LocaleMessageSourceService msgService;
	
	/**
	 * iosUserService
	 */
	@Autowired
	private IosUserService iosUserService;
	
	/**
	 * sysUserService
	 */
	@Autowired
	private SysUserService sysUserService;
	
	@Autowired
	private SysGlobalConfigService configService;

	/**
	 * 字典用Serviceを取得する。
	 *
	 * @return
	 */
	@Autowired
	public SysDictionaryService sysDictionaryService;
	
	/**
	 * 手机端客户注册
	 * @param inputDto
	 * @param result
	 * @return
	 */
	@PostMapping(value="customer/register")
	@ApiOperation(value = "手机端客户注册",httpMethod="POST")
	public ClientResponseJsonBean  registerCustomer(
			@ApiParam @Valid @RequestBody ServerRequestJsonBean<IosCustomerInputDto> inputDto,BindingResult result) {
		//初始化响应对象
		ClientResponseJsonBean response = null;
		//校验判断是否存在error
		if(!result.hasErrors()) {
			//判断账号是否存在
			String customerName = inputDto.getData().getCustomerName();
			String companyName = inputDto.getData().getCustomerCompany();
		  	if(sysUserService.getCustomerByCustomerName(customerName,companyName)==0) {
				  sysUserService.insertCustomer(customerName,companyName);
			}
			response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.BUSR00004,
					msgService.getMessage(MessageCode.BUSR00004));

		} else {
			//校验error时
			// 返回信息设定
			response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}		
		return response;	
	}
	

	/**
	 * 手机端客户信息一览
	 * @return
	 */
	@RequestMapping(value = "customer/list", method = RequestMethod.GET)
	@ApiOperation(value = "手机端客户信息一览", httpMethod = "GET")
	public ClientResponseJsonBean getCustomerList() {
		
		//	初始化响应对象
		ClientResponseJsonBean response = null;
		CustomerSearchInputDto input = new CustomerSearchInputDto();
		String userId = MDC.get(MDCFilter.USER_ID);
		input.setLeaderId(userId);
		//	获取会员信息
		List<SysCustomer> customerList = sysUserService.searchCustomer(input);
		response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
					msgService.getMessage(MessageCode.SCOM00001),customerList);
		return response;	
	}

	/**
	 * 删除客户信息
	 * @param inputDto
	 * @param result
	 * @return
	 */
	@PostMapping(value="customer/delete")
	@ApiOperation(value = "删除客户信息",httpMethod="POST")
	public ClientResponseJsonBean  deleteCustomer(
			@ApiParam @Valid @RequestBody ServerRequestJsonBean<IosCustomerUpdateDto> inputDto,BindingResult result) {
		//初始化响应对象
		ClientResponseJsonBean response = null;
		//校验判断是否存在error
		if(!result.hasErrors()) {
			//删除客户信息
			String customerId = inputDto.getData().getCustomerId();
			sysUserService.delSysCustomerByCustomerId(customerId);
			response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
					msgService.getMessage(MessageCode.SCOM00001));

		} else {
			//校验error时
			// 返回信息设定
			response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}
		return response;
	}
}
