package jp.co.nttdata.sz.web.service.category;

import jp.co.nttdata.sz.web.entity.MultipleMstProductsCategory;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 商品分类Service接口
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface IMultipleMstProductsCategoryService
{
    /**
     * 查询商品分类
     *
     * @param id 商品分类主键
     * @return 商品分类
     */
    public MultipleMstProductsCategory selectMultipleMstProductsCategoryById(Long id, String storeId);

    /**
     * 查询商品分类列表
     *
     * @param multipleMstProductsCategory 商品分类
     * @return 商品分类集合
     */
    public List<MultipleMstProductsCategory> selectMultipleMstProductsCategoryList(MultipleMstProductsCategory multipleMstProductsCategory);

    /**
     * 新增商品分类
     *
     * @param multipleMstProductsCategory 商品分类
     * @return 结果
     */
    public int insertMultipleMstProductsCategory(MultipleMstProductsCategory multipleMstProductsCategory);

    /**
     * 修改商品分类
     *
     * @param multipleMstProductsCategory 商品分类
     * @return 结果
     */
    public int updateMultipleMstProductsCategory(MultipleMstProductsCategory multipleMstProductsCategory);

    /**
     * 批量删除商品分类
     *
     * @param ids 需要删除的商品分类主键集合
     * @return 结果
     */
    public int deleteMultipleMstProductsCategoryByIds(Long[] ids,String storeId);

    /**
     * 删除商品分类信息
     *
     * @param id 商品分类主键
     * @return 结果
     */
    public int deleteMultipleMstProductsCategoryById(Long id,String storeId);

    }
