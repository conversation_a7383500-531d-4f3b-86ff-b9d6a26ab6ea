package jp.co.nttdata.sz.web.controller.actfollow.dto;

public class ActivityListOutputDto {

    /**
     * 用户
     */
    private String userName;

    /**
     * sessionId
     */
    private String sessionId;

    /**
     * 状态
     */
    private String actStatus;

    /**
     * 进店时间
     */
    private String userStartTime;

    /**
     * 出店时间
     */
    private String userEndTime;

    /**
     * 确认人员
     */
    private String followName;

	/**
	 * 订单分类
	 */
	private String settlementType;

	/**
	 * 订单ID
	 */
	private String orderId;

	/**
	 * 订单状态
	 */
	private String orderStatus;

	/**
	 * 订单金额
	 */
	private String amount;

	/**
	 * 订单处理时长
	 */
	private String processTime;

	/**
	 * 活动ID
	 */
	private String shopcarId;

	/**
	 * 退货申请id
	 */
	private String salesReturnId;


	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public String getActStatus() {
		return actStatus;
	}

	public void setActStatus(String actStatus) {
		this.actStatus = actStatus;
	}

	public String getUserStartTime() {
		return userStartTime;
	}

	public void setUserStartTime(String userStartTime) {
		this.userStartTime = userStartTime;
	}

	public String getUserEndTime() {
		return userEndTime;
	}

	public void setUserEndTime(String userEndTime) {
		this.userEndTime = userEndTime;
	}

	public String getFollowName() {
		return followName;
	}

	public void setFollowName(String followName) {
		this.followName = followName;
	}

	public String getSettlementType() {
		return settlementType;
	}

	public void setSettlementType(String settlementType) {
		this.settlementType = settlementType;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getProcessTime() {
		return processTime;
	}

	public void setProcessTime(String processTime) {
		this.processTime = processTime;
	}

	public String getShopcarId() {
		return shopcarId;
	}

	public void setShopcarId(String shopcarId) {
		this.shopcarId = shopcarId;
	}

	public String getSalesReturnId() {
		return salesReturnId;
	}

	public void setSalesReturnId(String salesReturnId) {
		this.salesReturnId = salesReturnId;
	}
}
