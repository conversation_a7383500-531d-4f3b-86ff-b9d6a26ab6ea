package jp.co.nttdata.sz.web.service.salesreturn.dto;

import org.thanos.core.socket.server.BaseSocketMessage;

public class SalesReturnCompleteOptDto extends BaseSocketMessage {
	
	/**'
	 * 退货订单id
	 */
	private String returnId;
	

	private String status;


	public String getReturnId() {
		return returnId;
	}

	public void setReturnId(String returnId) {
		this.returnId = returnId;
	}


	public String getStatus() {
		return status;
	}


	public void setStatus(String status) {
		this.status = status;
	}
	
	

}
