package jp.co.nttdata.sz.web.service.category.impl;


import jp.co.nttdata.sz.web.entity.MultipleMstProductsCategory;
import jp.co.nttdata.sz.web.mapper.MultipleMstProductsCategoryMapper;
import jp.co.nttdata.sz.web.service.category.IMultipleMstProductsCategoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class MultipleMstProductsCategoryServiceImpl implements IMultipleMstProductsCategoryService {
    @Resource
    private MultipleMstProductsCategoryMapper multipleMstProductsCategoryMapper;

    /**
     * 查询商品分类
     *
     * @param id 商品分类主键
     * @return 商品分类
     */
    @Override
    public MultipleMstProductsCategory selectMultipleMstProductsCategoryById(Long id, String storeId) {
        return multipleMstProductsCategoryMapper.selectMultipleMstProductsCategoryById(id, storeId);
    }

    /**
     * 查询商品分类列表
     *
     * @param multipleMstProductsCategory 商品分类
     * @return 商品分类
     */
    @Override
    public List<MultipleMstProductsCategory> selectMultipleMstProductsCategoryList(MultipleMstProductsCategory multipleMstProductsCategory) {
        return multipleMstProductsCategoryMapper.selectMultipleMstProductsCategoryList(multipleMstProductsCategory);
    }

    /**
     * 新增商品分类
     *
     * @param multipleMstProductsCategory 商品分类
     * @return 结果
     */
    @Override
    public int insertMultipleMstProductsCategory(MultipleMstProductsCategory multipleMstProductsCategory) {
        return multipleMstProductsCategoryMapper.insertMultipleMstProductsCategory(multipleMstProductsCategory);
    }

    /**
     * 修改商品分类
     *
     * @param multipleMstProductsCategory 商品分类
     * @return 结果
     */
    @Override
    public int updateMultipleMstProductsCategory(MultipleMstProductsCategory multipleMstProductsCategory) {
        return multipleMstProductsCategoryMapper.updateMultipleMstProductsCategory(multipleMstProductsCategory);
    }

    /**
     * 批量删除商品分类
     *
     * @param ids 需要删除的商品分类主键
     * @return 结果
     */
    @Override
    public int deleteMultipleMstProductsCategoryByIds(Long[] ids, String storeId) {
        return multipleMstProductsCategoryMapper.deleteMultipleMstProductsCategoryByIds(ids, storeId);
    }

    /**
     * 删除商品分类信息
     *
     * @param id 商品分类主键
     * @return 结果
     */
    @Override
    public int deleteMultipleMstProductsCategoryById(Long id, String storeId) {
        return multipleMstProductsCategoryMapper.deleteMultipleMstProductsCategoryById(id, storeId);
    }


}
