package jp.co.nttdata.sz.web.service.orderopt.dto;

import java.time.LocalDateTime;
import java.util.List;

import jp.co.nttdata.sz.bj.service.dto.BJTrackOutputDto;
import jp.co.nttdata.sz.web.entity.MstOrder;

/**
 * 订单购物车明细
 * <AUTHOR>
 *
 */
public class OrderShopcarInfoDto {

	/**
	 * 订单信息
	 */
	private MstOrder orderInfo;
	
	/**
	 * 进店时间
	 */
	private LocalDateTime enterTime ;
	
	/**
	 * 离店时间
	 */
	private LocalDateTime leaveTime ;
	
	/**
	 * 订单购物车明细
	 */
	private List<OrderShopcarTimeDto>  timeItems;
	
	private BJTrackOutputDto trackData;

	public MstOrder getOrderInfo() {
		return orderInfo;
	}

	public void setOrderInfo(MstOrder orderInfo) {
		this.orderInfo = orderInfo;
	}

	public List<OrderShopcarTimeDto> getTimeItems() {
		return timeItems;
	}

	public void setTimeItems(List<OrderShopcarTimeDto> timeItems) {
		this.timeItems = timeItems;
	}

	public LocalDateTime getEnterTime() {
		return enterTime;
	}

	public void setEnterTime(LocalDateTime enterTime) {
		this.enterTime = enterTime;
	}

	public LocalDateTime getLeaveTime() {
		return leaveTime;
	}

	public void setLeaveTime(LocalDateTime leaveTime) {
		this.leaveTime = leaveTime;
	}

	public BJTrackOutputDto getTrackData() {
		return trackData;
	}

	public void setTrackData(BJTrackOutputDto trackData) {
		this.trackData = trackData;
	}
	
	
	
	
}
