package jp.co.nttdata.sz.web.controller.settlement.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jp.co.nttdata.sz.web.entity.OrderSettlement;
import org.springframework.format.annotation.DateTimeFormat;
import org.thanos.core.page.bean.PageBean;
import org.thanos.core.page.bean.PageWrapperBean;

public class SettlementSearchInputDto extends OrderSettlement {
    /** 用户名 */
    private String userName;
    /**
     * 结算状态 1-结算 0-未结算
     */
    private Integer setlementStatus;

    /** 创建起始日 */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private String startTime;

    /** 创建终止日 */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private String endTime;

    /** 排序规则 */
    private int orderCondition;

    /** 部门 */
    private String deptId;

    private String storeId;

    /**
     * 分页对象
     */
    private PageBean page;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public PageBean getPage() {
        return page;
    }

    public void setPage(PageBean page) {
        this.page = page;
    }

    public Integer getSetlementStatus() {
        return setlementStatus;
    }

    public void setSetlementStatus(Integer setlementStatus) {
        this.setlementStatus = setlementStatus;
    }

    public int getOrderCondition() {
        return orderCondition;
    }

    public void setOrderCondition(int orderCondition) {
        this.orderCondition = orderCondition;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }
}
