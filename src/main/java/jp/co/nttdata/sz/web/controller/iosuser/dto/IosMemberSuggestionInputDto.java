package jp.co.nttdata.sz.web.controller.iosuser.dto;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * ios手机端用户反馈入力dto
 * <AUTHOR>
 *
 */
public class IosMemberSuggestionInputDto {

	/**
	 * storeId
	 */
	@NotNull(message="storeId can not be null")
	private String storeId;

	/**
	 * 反馈类型 ： suggestion 建议 / issue 问题
	 */
	private String suggestionType;
	
	/**
	 * 反馈内容
	 */
	@NotNull(message="message can not be null")
	private String message;

	private String phone;

	/**
	 * 反馈tag
	 */
//	@NotNull(message="tag can not be null")
	private String feedbackTags;

	/**
	 * 反馈图片
	 */
	private ArrayList<String> imagePaths;

	public String getSuggestionType() {
		return suggestionType;
	}

	public void setSuggestionType(String suggestionType) {
		this.suggestionType = suggestionType;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public ArrayList<String> getImagePaths() {
		return imagePaths;
	}

	public void setImagePaths(ArrayList<String> imagePaths) {
		this.imagePaths = imagePaths;
	}

	public String getFeedbackTags() {
		return feedbackTags;
	}

	public void setFeedbackTags(String feedbackTags) {
		this.feedbackTags = feedbackTags;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}
}
