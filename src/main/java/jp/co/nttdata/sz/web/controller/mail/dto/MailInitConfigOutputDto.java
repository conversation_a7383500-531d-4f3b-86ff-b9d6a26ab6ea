package jp.co.nttdata.sz.web.controller.mail.dto;

public class MailInitConfigOutputDto {

	/**
	 * 邮箱服务器站点
	 */
	private  String mailHost;
	
	/**
	 * 邮箱服务器协议
	 */
	private String mailProtocol;
	
	/**
	 * 邮箱端口
	 */
	private String mailPort;
	
	/**
	 * 邮箱用户名密码
	 */
	private String mailUserName;
	
	/**
	 * 邮箱授权码
	 */
	private String password;

	public String getMailHost() {
		return mailHost;
	}

	public String getMailProtocol() {
		return mailProtocol;
	}

	public String getMailPort() {
		return mailPort;
	}

	public String getMailUserName() {
		return mailUserName;
	}

	public String getPassword() {
		return password;
	}

	public void setMailHost(String mailHost) {
		this.mailHost = mailHost;
	}

	public void setMailProtocol(String mailProtocol) {
		this.mailProtocol = mailProtocol;
	}

	public void setMailPort(String mailPort) {
		this.mailPort = mailPort;
	}

	public void setMailUserName(String mailUserName) {
		this.mailUserName = mailUserName;
	}

	public void setPassword(String password) {
		this.password = password;
	}
	
	
	
}
