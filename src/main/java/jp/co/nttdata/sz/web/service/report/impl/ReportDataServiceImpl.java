package jp.co.nttdata.sz.web.service.report.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.co.nttdata.sz.web.controller.report.dto.DailyReportSearchInputDto;
import jp.co.nttdata.sz.web.controller.report.dto.ReportRemarkInputDto;
import jp.co.nttdata.sz.web.dao.ReportViewDao;
import jp.co.nttdata.sz.web.entity.DailyReportEntity;
import jp.co.nttdata.sz.web.service.report.ReportDataService;

@Service
public class ReportDataServiceImpl implements ReportDataService{

	@Autowired
	private ReportViewDao reportViewDao;
	
	@Override
	public List<DailyReportEntity> searchDailyReportData(DailyReportSearchInputDto inputDto) {
		List<DailyReportEntity> reportList = reportViewDao.searchDailyReprot(inputDto);
		return reportList;
	}

	@Override
	public int updateRemarkData(ReportRemarkInputDto inputDto) {
		int ret=0;
		if(reportViewDao.hasRemark(inputDto.getShopcarId())) {
			ret+=reportViewDao.updateRemarkData(inputDto);
		} else {
			ret+=reportViewDao.insertRemarkData(inputDto);
		}
		return ret;
	}
	

}
