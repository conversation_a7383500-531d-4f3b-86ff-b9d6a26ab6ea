package jp.co.nttdata.sz.web.service.datasync.dto;
import java.util.List;

public class Items {
    private int id;
    private String itemId;
    private int quantity;
    private String title;
    private int organizationUnitId;
	private String auditStatus;
    private String subTitle;
    private double price;
    private double promPrice;
    private String barcode;
    private int salesVolume;
    private String keywords;
    private String picUrl;
    private String likeCount;
    private String description;
    private boolean isFromBrand;
    private String sellerId;
    private String outerId;
    private String fromType;
    private boolean hasRealSkus;
    private List<Propimgs> propImgs;
    private int[] categoryIds;
    private int[] propValueIds;
    private int[] tagIds;
    private List<Skus> skus;
    private List<ItemImagesOrVideos> itemImagesOrVideos;
    private List<Onlinestoreinfos> onlineStoreInfos;
    private String ageScope;
    private String gender;
    private String rfidCode;
    private String price2;
    private String groupQrCodeInfo;
    private int orderNumbe;
    private int brandId;
    private String language;
    private String region;
    private String pointRedeemType;
    private List<PointRule> pointRul;

	private String belongStore;

	public String getBelongStore() {
		return belongStore;
	}

	public void setBelongStore(String belongStore) {
		this.belongStore = belongStore;
	}

	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getItemId() {
		return itemId;
	}
	public void setItemId(String itemId) {
		this.itemId = itemId;
	}
	public int getQuantity() {
		return quantity;
	}
	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public int getOrganizationUnitId() {
		return organizationUnitId;
	}
	public void setOrganizationUnitId(int organizationUnitId) {
		this.organizationUnitId = organizationUnitId;
	}

	public String getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(String auditStatus) {
		this.auditStatus = auditStatus;
	}

	public String getSubTitle() {
		return subTitle;
	}
	public void setSubTitle(String subTitle) {
		this.subTitle = subTitle;
	}
	public double getPrice() {
		return price;
	}
	public void setPrice(double price) {
		this.price = price;
	}
	public double getPromPrice() {
		return promPrice;
	}
	public void setPromPrice(double promPrice) {
		this.promPrice = promPrice;
	}
	public String getBarcode() {
		return barcode;
	}
	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}
	public int getSalesVolume() {
		return salesVolume;
	}
	public void setSalesVolume(int salesVolume) {
		this.salesVolume = salesVolume;
	}
	public String getKeywords() {
		return keywords;
	}
	public void setKeywords(String keywords) {
		this.keywords = keywords;
	}
	public String getPicUrl() {
		return picUrl;
	}
	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}
	public String getLikeCount() {
		return likeCount;
	}
	public void setLikeCount(String likeCount) {
		this.likeCount = likeCount;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public boolean isFromBrand() {
		return isFromBrand;
	}
	public void setFromBrand(boolean isFromBrand) {
		this.isFromBrand = isFromBrand;
	}
	public String getSellerId() {
		return sellerId;
	}
	public void setSellerId(String sellerId) {
		this.sellerId = sellerId;
	}
	public String getOuterId() {
		return outerId;
	}
	public void setOuterId(String outerId) {
		this.outerId = outerId;
	}
	public String getFromType() {
		return fromType;
	}
	public void setFromType(String fromType) {
		this.fromType = fromType;
	}
	public boolean isHasRealSkus() {
		return hasRealSkus;
	}
	public void setHasRealSkus(boolean hasRealSkus) {
		this.hasRealSkus = hasRealSkus;
	}
	public List<Propimgs> getPropImgs() {
		return propImgs;
	}
	public void setPropImgs(List<Propimgs> propImgs) {
		this.propImgs = propImgs;
	}
	public int[] getCategoryIds() {
		return categoryIds;
	}
	public void setCategoryIds(int[] categoryIds) {
		this.categoryIds = categoryIds;
	}
	public int[] getPropValueIds() {
		return propValueIds;
	}
	public void setPropValueIds(int[] propValueIds) {
		this.propValueIds = propValueIds;
	}
	public int[] getTagIds() {
		return tagIds;
	}
	public void setTagIds(int[] tagIds) {
		this.tagIds = tagIds;
	}
	public List<Skus> getSkus() {
		return skus;
	}
	public void setSkus(List<Skus> skus) {
		this.skus = skus;
	}
	public List<ItemImagesOrVideos> getItemImagesOrVideos() {
		return itemImagesOrVideos;
	}
	public void setItemImagesOrVideos(List<ItemImagesOrVideos> itemImagesOrVideos) {
		this.itemImagesOrVideos = itemImagesOrVideos;
	}
	public List<Onlinestoreinfos> getOnlineStoreInfos() {
		return onlineStoreInfos;
	}
	public void setOnlineStoreInfos(List<Onlinestoreinfos> onlineStoreInfos) {
		this.onlineStoreInfos = onlineStoreInfos;
	}
	public String getAgeScope() {
		return ageScope;
	}
	public void setAgeScope(String ageScope) {
		this.ageScope = ageScope;
	}
	public String getGender() {
		return gender;
	}
	public void setGender(String gender) {
		this.gender = gender;
	}
	public String getRfidCode() {
		return rfidCode;
	}
	public void setRfidCode(String rfidCode) {
		this.rfidCode = rfidCode;
	}
	public String getPrice2() {
		return price2;
	}
	public void setPrice2(String price2) {
		this.price2 = price2;
	}
	public String getGroupQrCodeInfo() {
		return groupQrCodeInfo;
	}
	public void setGroupQrCodeInfo(String groupQrCodeInfo) {
		this.groupQrCodeInfo = groupQrCodeInfo;
	}
	public int getOrderNumbe() {
		return orderNumbe;
	}
	public void setOrderNumbe(int orderNumbe) {
		this.orderNumbe = orderNumbe;
	}
	public int getBrandId() {
		return brandId;
	}
	public void setBrandId(int brandId) {
		this.brandId = brandId;
	}
	public String getLanguage() {
		return language;
	}
	public void setLanguage(String language) {
		this.language = language;
	}
	public String getRegion() {
		return region;
	}
	public void setRegion(String region) {
		this.region = region;
	}
	public String getPointRedeemType() {
		return pointRedeemType;
	}
	public void setPointRedeemType(String pointRedeemType) {
		this.pointRedeemType = pointRedeemType;
	}
	public List<PointRule> getPointRul() {
		return pointRul;
	}
	public void setPointRul(List<PointRule> pointRul) {
		this.pointRul = pointRul;
	}
}
