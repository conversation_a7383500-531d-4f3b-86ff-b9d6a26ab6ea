package jp.co.nttdata.sz.web.controller.batchuse.dto;

import org.thanos.core.socket.server.BaseSocketMessage;

import jp.co.nttdata.sz.web.service.actfollow.dto.ActivityFollowInfoDto;

public class ActivityCreatWrapperDto  extends BaseSocketMessage{
	
	
	private ActivityFollowInfoDto newActivity;

	public ActivityFollowInfoDto getNewActivity() {
		return newActivity;
	}

	public void setNewActivity(ActivityFollowInfoDto newActivity) {
		this.newActivity = newActivity;
	}
	

}
