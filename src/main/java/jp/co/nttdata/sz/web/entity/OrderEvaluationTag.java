package jp.co.nttdata.sz.web.entity;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.time.LocalDateTime;

/**
 * 订单评价状态对象 order_evaluation_tag
 * 
 * <AUTHOR>
 * @date 2021-10-12
 */
public class OrderEvaluationTag
{
    private static final long serialVersionUID = 1L;

    /** 主键Id */
    private Integer id;

    /** 订单Id */
    private String orderId;

    /** 评价Id */
    private Integer tagId;

    /** 创建日期 */
    private LocalDateTime createTime;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setOrderId(String orderId)
    {
        this.orderId = orderId;
    }

    public String getOrderId()
    {
        return orderId;
    }
    public void setTagId(Integer tagId) 
    {
        this.tagId = tagId;
    }

    public Integer getTagId() 
    {
        return tagId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("tagId", getTagId())
            .append("createTime", getCreateTime())
            .toString();
    }
}
