package jp.co.nttdata.sz.web.controller.batchuse.dto;

/**
 * socketServer画面使用区域枚举
 * 
 * <AUTHOR>
 *
 */
public enum UseAreaEnum {

	/**
	 * 上菜单通知用
	 */
	PC_TOP("top"),
	/**
	 * 左菜单通知用
	 */
	PC_MENU("menu"),
	/**
	 * 工作台页面用
	 */
	PC_DASHBOARD("dashboard"),
	/**
	 * 购物轨迹画面用
	 */
	PC_TRACK("track"),
	/**
	 * 活动跟踪画面用
	 */
	PC_ACTIVITY_FOLLOW("actfollow"),

	/**
	 * 活动跟踪画面用
	 */
	PC_WARN_MESSAGE("warnMessage");

	private String useArea;

	private UseAreaEnum(String useArea) {
		this.setUseArea(useArea);
	}

	public String getUseArea() {
		return useArea;
	}

	public void setUseArea(String useArea) {
		this.useArea = useArea;
	}

}
