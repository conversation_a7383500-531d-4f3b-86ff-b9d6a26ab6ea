package jp.co.nttdata.sz.web.service.analysis.dto;

import java.util.List;

import jp.co.nttdata.sz.web.controller.menu.dto.MenuCountOutputDto;
import jp.co.nttdata.sz.web.entity.MemberInfoEntity;
import jp.co.nttdata.sz.web.entity.MstOrder;
import jp.co.nttdata.sz.web.service.actfollow.dto.ActivityFollowInfoDto;
import jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesAmountDto;
import jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesOutputDto;
import jp.co.nttdata.sz.web.service.salesreturn.dto.SalesReturnSearchOutputDto;
import lombok.Data;

@Data
public class DashBoardInfoDto {
	/**
	 * 待审核会员list
	 */
	private List<MemberInfoEntity> MemberInfoList;

	/**
	 * 退货订单list
	 */
	private List<SalesReturnSearchOutputDto> outPutList;

	/**
	 * 待处理订单list
	 */
	private List<MstOrder> retList;

	/**
	 * 本日の販売
	 */
	private Integer todayAmount;

	/**
	 * 昨日の売上
	 */
	private Integer yesterdayAmount;

	/**
	 * 前一周
	 */
	private WeekSalesOutputDto nowWeekSaleInfo;
	private WeekSalesOutputDto nowMonthSaleInfo;
	private WeekSalesOutputDto nowHoursSaleInfo;

	private List<ActivityFollowInfoDto> actList;

	private MenuCountOutputDto menuCountData;
}
