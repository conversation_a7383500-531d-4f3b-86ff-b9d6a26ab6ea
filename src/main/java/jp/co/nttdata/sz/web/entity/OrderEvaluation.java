package jp.co.nttdata.sz.web.entity;

import java.time.LocalDateTime;

/**
 * 订单评价
 * <AUTHOR>
 */
public class OrderEvaluation {
	   /**	订单编号 */
	   private String orderId;

	   /**	订单评价 */
	   private String orderEvaluation;

	   /**	订单评级 */
	   private Integer evaluationStar;

	   /**	订单创建时间 */
	   private LocalDateTime createTime;

	   /**	店铺ID */
	   private String storeId;

	   /**	店铺名 */
	   private String storeName;
	   
	   /**	用户ID */
	   private String userId;
	   
	   /**	登陆用户名 */
	   private String userAccount;
	   
	   /**	用户姓名/厂家名称 */
	   private String userName;

	   /**	评价时间 */
	   private LocalDateTime evaluationTime;

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getOrderEvaluation() {
		return orderEvaluation;
	}

	public void setOrderEvaluation(String orderEvaluation) {
		this.orderEvaluation = orderEvaluation;
	}

	public Integer getEvaluationStar() {
		return evaluationStar;
	}

	public void setEvaluationStar(Integer evaluationStar) {
		this.evaluationStar = evaluationStar;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public String getStoreName() {
		return storeName;
	}

	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}

    public void setCreateTime(LocalDateTime createTime) {
       this.createTime = createTime;
    }

	public LocalDateTime getCreateTime() {
	    return createTime;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserAccount() {
		return userAccount;
	}

	public void setUserAccount(String userAccount) {
		this.userAccount = userAccount;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public LocalDateTime getEvaluationTime() {
		return evaluationTime;
	}

	public void setEvaluationTime(LocalDateTime evaluationTime) {
		this.evaluationTime = evaluationTime;
	}
	   
	   
}
