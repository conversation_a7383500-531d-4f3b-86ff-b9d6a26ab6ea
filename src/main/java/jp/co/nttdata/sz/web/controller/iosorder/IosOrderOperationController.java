package jp.co.nttdata.sz.web.controller.iosorder;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jp.co.nttdata.sz.api.entity.ShopMessageEntity;
import jp.co.nttdata.sz.api.service.message.ShopManagerMessageService;
import jp.co.nttdata.sz.web.controller.iosorder.dto.IosOrderEvaluateIdInputDto;
import jp.co.nttdata.sz.web.controller.iosorder.dto.IosOrderEvaluateInputDto;
import jp.co.nttdata.sz.web.controller.iosorder.dto.IosOrderReturnConsumerDto;
import jp.co.nttdata.sz.web.controller.iosorder.dto.IosOrderReturnWrapperDto;
import jp.co.nttdata.sz.web.entity.OrderEvaluationTag;
import jp.co.nttdata.sz.web.service.evaluationTag.impl.OrderEvaluationTagServiceImpl;
import jp.co.nttdata.sz.web.service.iosorder.IosOrderEvaluateService;
import jp.co.nttdata.sz.web.service.iosorder.dto.OrdereValuateInputDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thanos.core.message.LocaleMessageSourceService;
import org.thanos.core.message.MessageCode;
import org.thanos.platform.fw.constants.CodeConstant;
import org.thanos.platform.fw.model.ClientResponseJsonBean;
import org.thanos.platform.fw.model.ServerRequestJsonBean;
import org.thanos.platform.util.ResultDataUtil;
import org.thanos.platform.util.StringUtil;
import org.thanos.platform.util.VaildErrorUtil;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * ios手机端订单Controller
 * <AUTHOR>
 *
 */
@Api(value = "手机端订单controller")
@RequestMapping("ios")
@RestController
public class IosOrderOperationController {

	/**
	 * msg
	 */
	@Autowired
	private LocaleMessageSourceService msgService;

	/**
	 * iosOrderService
	 */
	@Autowired
	private IosOrderEvaluateService iosOrderService;

	/**
	 * iosOrderService
	 */
	@Autowired
	private OrderEvaluationTagServiceImpl orderEvaluationTagService;

	/**
	 * mq
	 */
	@Autowired
	private ShopManagerMessageService shopManagerMessageService;

	// 消费者申请退货
	@PostMapping(value="order/returnConsumer")
	public ClientResponseJsonBean returnConsumer(
			@ApiParam @Valid @RequestBody ServerRequestJsonBean<IosOrderReturnConsumerDto> inputDto, BindingResult result) {
		//	初始化响应对象
		ClientResponseJsonBean response = null;
		//	校验判断是否存在error
		if(!result.hasErrors()) {
			//	入力check成功
			IosOrderReturnConsumerDto consumerData = inputDto.getData();
			//	订单退货
			int ret = iosOrderService.realReturnConsumerOrder(consumerData);
			if(ret == -1) {
				//	订单不存在
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20106,
						msgService.getMessage(MessageCode.BORD20106));
			}else if(ret == 0) {
				//	退货申请失败
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20108,
						msgService.getMessage(MessageCode.BORD20108));
			}else {
				//	退货申请成功
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
						msgService.getMessage(MessageCode.SCOM00001));
			}
		}else {
			//	入力check失败
			//	返回信息设定
			response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}
		return response;
	}

	@PostMapping(value="order/evaluate")
	@ApiOperation(value = "手机端订单评价",httpMethod="POST")
	public ClientResponseJsonBean evaluateOrder(
			@ApiParam @Valid @RequestBody ServerRequestJsonBean<IosOrderEvaluateInputDto> inputDto,BindingResult result) {
		//	初始化响应对象
		ClientResponseJsonBean response = null;
		//	校验判断是否存在error
		if(!result.hasErrors()) {
			//	入力check成功
			IosOrderEvaluateInputDto inputData = inputDto.getData();
			OrdereValuateInputDto inputBean = new OrdereValuateInputDto();
			inputBean.setOrderId(inputData.getOrderId());
			inputBean.setEvaluatetionStar(inputData.getEvaluatetionStar());
			inputBean.setEvaluationTags(inputDto.getData().getEvaluationTags());
			inputBean.setEvaluationContent(inputData.getEvaluationContent());
			//	订单评价
			int ret = iosOrderService.evaluateOrder(inputBean);

			if(ret == -1) {
				//	订单不存在
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20106,
						msgService.getMessage(MessageCode.BORD20106));
			}else if(ret == 0) {
				//	评价失败
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20107,
						msgService.getMessage(MessageCode.BORD20107));
			}else {
				//	评价成功
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.BORD00005,
						msgService.getMessage(MessageCode.BORD00005), null);
			}
		}else {
			//	入力check失败
			//	返回信息设定
			response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}
		return response;

	}

	@PostMapping(value="order/newEvaluate")
	@ApiOperation(value = "手机端订单评价",httpMethod="POST")
	public ClientResponseJsonBean newEvaluateOrder(
			@ApiParam @Valid @RequestBody ServerRequestJsonBean<IosOrderEvaluateIdInputDto> inputDto, BindingResult result) {
		//	初始化响应对象
		ClientResponseJsonBean response = null;
		//	校验判断是否存在error
		if(!result.hasErrors()) {
			//	入力check成功
			IosOrderEvaluateIdInputDto inputData = inputDto.getData();
			OrdereValuateInputDto inputBean = new OrdereValuateInputDto();
			inputBean.setOrderId(inputData.getOrderId());
			inputBean.setEvaluatetionStar(inputData.getEvaluatetionStar());
//			inputBean.setEvaluationTags(inputDto.getData().getEvaluationTags());
			inputBean.setEvaluationContent(inputData.getEvaluationContent());

			// 订单结算评价
			if(inputData.getEvaluationId() != null){
				List<String> tagName = new ArrayList<String>();
				tagName.add(inputData.getEvaluationTagsName());
				inputBean.setEvaluationTags(tagName);

				// 插入订单评价表
				OrderEvaluationTag orderEvaluationTag = new OrderEvaluationTag();
				orderEvaluationTag.setOrderId(inputData.getOrderId());
				orderEvaluationTag.setTagId(inputData.getEvaluationId());
				orderEvaluationTag.setCreateTime(LocalDateTime.now());
				orderEvaluationTagService.insertOrderEvaluationTag(orderEvaluationTag);
			}

			//	订单评价
			int ret = iosOrderService.evaluateOrder(inputBean);

			if(ret == -1) {
				//	订单不存在
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20106,
						msgService.getMessage(MessageCode.BORD20106));
			}else if(ret == 0) {
				//	评价失败
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20107,
						msgService.getMessage(MessageCode.BORD20107));
			}else {
				//	评价成功
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.BORD00005,
						msgService.getMessage(MessageCode.BORD00005), null);
				String sessionId = orderEvaluationTagService.getSessionByOrderId(inputData.getOrderId());
				if (StringUtil.isNotEmpty(sessionId)) {
					inputData.setSessionId(sessionId);
				}

				if(inputData.getEvaluationTagsName().contains("多算")){
					//发送MQ
					shopManagerMessageService.sendMessageToQueue(ShopMessageEntity.buildMessageEntity(ShopMessageEntity.MessageType.STATICS_ORDER_FAIL_MORE,null,inputData));
				}
				if(inputData.getEvaluationTagsName().contains("少算")){
					shopManagerMessageService.sendMessageToQueue(ShopMessageEntity.buildMessageEntity(ShopMessageEntity.MessageType.STATICS_ORDER_FAIL_LESS,null,inputData));
				}

			}
		}else {
			//	入力check失败
			//	返回信息设定
			response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}
		return response;

	}


	@PostMapping(value="order/return")
	@ApiOperation(value = "手机端申请退货",httpMethod="POST")
	public ClientResponseJsonBean returnOrder(
			@ApiParam @Valid @RequestBody ServerRequestJsonBean<IosOrderReturnWrapperDto> inputDto,BindingResult result) {
			//	初始化响应对象
			ClientResponseJsonBean response = null;
			//	校验判断是否存在error
			if(!result.hasErrors()) {
				//	入力check成功
				//	订单退货
				int ret = iosOrderService.realReturnOrder(inputDto.getData());
				if(ret == -1) {
					//	订单不存在
					response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20106,
							msgService.getMessage(MessageCode.BORD20106));
				}else if(ret == 0) {
					//	退货申请失败
					response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20108,
							msgService.getMessage(MessageCode.BORD20108));
				}else {
					//	退货申请成功
					response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
							msgService.getMessage(MessageCode.SCOM00001));
				}
			}else {
				//	入力check失败
				//	返回信息设定
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
						msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
			}
			return response;
	}
}
