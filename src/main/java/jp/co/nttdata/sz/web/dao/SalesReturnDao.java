package jp.co.nttdata.sz.web.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import jp.co.nttdata.sz.web.entity.SalesReturnInfoEntity;
import jp.co.nttdata.sz.web.entity.SalesReturnItemEntity;
import jp.co.nttdata.sz.web.entity.WeixinReturnItemEntity;
import jp.co.nttdata.sz.web.service.activiti.dto.ReturnSearchInputDto;
import jp.co.nttdata.sz.web.service.salesreturn.dto.ReturnInfoOutputDto;
import jp.co.nttdata.sz.web.service.salesreturn.dto.SalesReturnSearchInputDto;
import jp.co.nttdata.sz.web.service.salesreturn.dto.SalesReturnSearchOutputDto;

public interface SalesReturnDao {

	public List<SalesReturnSearchOutputDto> searchSalesReturn(SalesReturnSearchInputDto entity);

	List<ReturnInfoOutputDto>   searchSalesReturnByCondition(ReturnSearchInputDto entity);

	public List<SalesReturnSearchOutputDto> getWaitDealSalesReturn(String storeId);

	public SalesReturnSearchOutputDto getSalesReturnInfoById(String returnId);

	public List<ReturnInfoOutputDto> getSalesReturnInfoByIds(@Param("returnIds") List<String> returnIds);

	public List<ReturnInfoOutputDto> getSalesReturnInfoByIdsAndCondtion(@Param("returnIds") List<String> returnIds,@Param("inputDto") ReturnSearchInputDto inputDto);

	public List<SalesReturnItemEntity> getReturnItemById(String returnId);

	public List<SalesReturnItemEntity> getSalesReturnItemById(String returnId);

	public List<SalesReturnItemEntity> getOrderReturnItemByOrderId(String returnId);


	public int insertSalesReturn(SalesReturnInfoEntity entity);

	public int insertSalesReturnItem(SalesReturnItemEntity itemEntity);

	public int insertWeixinReturnItem(WeixinReturnItemEntity entity);

	public int updateReturnStatus(SalesReturnInfoEntity entity);

	public int updateSalesReturnItem(SalesReturnItemEntity itemEntity);

	public int updateSalesReturnStatus(SalesReturnInfoEntity entity);

}
