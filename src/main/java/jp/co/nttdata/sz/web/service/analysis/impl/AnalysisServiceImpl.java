package jp.co.nttdata.sz.web.service.analysis.impl;

import jp.co.nttdata.sz.com.controller.CommonConst;
import jp.co.nttdata.sz.web.controller.analysis.dto.*;
import jp.co.nttdata.sz.web.controller.menu.dto.MenuCountOutputDto;
import jp.co.nttdata.sz.web.dao.AnalysisDao;
import jp.co.nttdata.sz.web.dao.MemberInfoDao;
import jp.co.nttdata.sz.web.entity.*;
import jp.co.nttdata.sz.web.service.actfollow.ActivityFollowService;
import jp.co.nttdata.sz.web.service.actfollow.dto.ActivityFollowInfoDto;
import jp.co.nttdata.sz.web.service.activiti.ActivitiOrderService;
import jp.co.nttdata.sz.web.service.activiti.ActivitiReturnOrderService;
import jp.co.nttdata.sz.web.service.analysis.AnalysisService;
import jp.co.nttdata.sz.web.service.analysis.dto.DashBoardInfoDto;
import jp.co.nttdata.sz.web.service.analysis.dto.DashBoardInfoDtoNew;
import jp.co.nttdata.sz.web.service.analysis.dto.RefundAmountInputDto;
import jp.co.nttdata.sz.web.service.orderopt.OrderOperationService;
import jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesAmountDto;
import jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesOutputDto;
import jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesStatusInputDto;
import jp.co.nttdata.sz.web.service.salesreturn.SalesReturnOperationService;
import jp.co.nttdata.sz.web.service.salesreturn.dto.SalesReturnSearchOutputDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;

@Service
public class AnalysisServiceImpl implements AnalysisService {

    /**
     * log
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    AnalysisDao analysisDao;


    /**
     * activiti 退货流程Service
     */
    @Autowired
    ActivitiReturnOrderService actReturnService;

    /**
     * 订单操作Service
     */
    @Autowired
    OrderOperationService orderService;

    /**
     * 退货订单操作Service
     */
    @Autowired
    SalesReturnOperationService salesReturnOperationService;

    /**
     * 会员管理dao
     */
    @Autowired
    private MemberInfoDao memberDao;

    @Autowired
    private ActivitiOrderService actOrderService;

    @Autowired
    private ActivityFollowService activityFollowService;

    /**
     * 商品销量排行获取
     */
    @Override
    public List<ProductInfoEntity> getProductSellInfo() {
        //检索商品销量排行榜
        List<ProductInfoEntity> productInfoEntitys = analysisDao.getproductInfo();
        return productInfoEntitys;
    }

    /**
     * 客流量信息获取
     */
    @Override
    public List<PassengerFlowEntity> getPassengerFlow(PassengerFlowInput passengerFlowInput) {
        List<PassengerFlowEntity> passengerFlowEntitys = new ArrayList<PassengerFlowEntity>();
        if (passengerFlowInput.getSearchType() == 1) {
            //检索一段时间内的客流量
            passengerFlowEntitys = analysisDao.getPassengerFlowByDay(passengerFlowInput);
        } else if (passengerFlowInput.getSearchType() == 0) {
            //检索一段时间内客流量
            passengerFlowEntitys = analysisDao.getPassengerFlowByHour(passengerFlowInput);
        } else if (passengerFlowInput.getSearchType() == 2) {
            //检索一段时间内客流量
            passengerFlowEntitys = analysisDao.getPassengerFlowByMonth(passengerFlowInput);
        }
        return passengerFlowEntitys;
    }

    /**
     * 获取商品库存信息
     */
    @Override
    public List<ProductInfo> getProductStockInfo() {
        List<ProductInfo> productInfo = new ArrayList<ProductInfo>();
        //获取商品库存信息
        productInfo = analysisDao.getProductStockInfo();
        return productInfo;
    }

    /**
     * 店铺数据获取（进店人数/订单数量/购买人次/销售额）
     */
    @Override
    public ShoppingInfoEntity getShoppingInfo(ShoppingInfoInput shoppingInfoInput) {
        //订单数量/购买人次/销售额获得
        ShoppingInfoEntity shoppingInfoEntity = analysisDao.getShoppingInfo(shoppingInfoInput);
        shoppingInfoEntity.setLastSalesVolume(analysisDao.getLastShoppingInfo(shoppingInfoInput));

        //进店人数获得
        ShoppingInfoEntity IntoShopPeoCount = analysisDao.getIntoShopPeoCount(shoppingInfoInput);
        if (null != IntoShopPeoCount) {
            shoppingInfoEntity.setPeopleCount(IntoShopPeoCount.getPeopleCount());
        }
        return shoppingInfoEntity;
    }

    /**
     * 店铺数据获取（一段时间内的订单数量）
     */
    @Override
    public List<OrderInfoEntity> getOrderInfo(OrderInfoInput orderInfoInput) {
        List<OrderInfoEntity> orderInfoEntity = null;
        if (0 == orderInfoInput.getSearchType()) {
            orderInfoEntity = analysisDao.getOrderInfo(orderInfoInput);
        } else if (1 == orderInfoInput.getSearchType()) {
            orderInfoEntity = analysisDao.getOrderInfoWeek(orderInfoInput);
        } else if (2 == orderInfoInput.getSearchType()) {
            orderInfoEntity = analysisDao.getOrderInfoMonth(orderInfoInput);
        }
        return orderInfoEntity;
    }

    /**
     * 店铺数据一览获取
     *
     * @param analysisInput
     * @return
     */
    public AnalysisOutput getShopInfo(AnalysisInput analysisInput) {
        AnalysisOutput analysisOutput = new AnalysisOutput();
        List<ProductInfoEntity> productInfoEntity = getProductSellInfo();
        List<PassengerFlowEntity> passengerFlowEntity = getPassengerFlow(analysisInput.getPassengerFlow());
        List<ProductInfo> productInfo = getProductStockInfo();
        ShoppingInfoEntity shoppingInfoEntity = getShoppingInfo(analysisInput.getShoppingInfo());
        List<OrderInfoEntity> orderInfoEntity = getOrderInfo(analysisInput.getOrderInfoInput());
        analysisOutput.setProductInfoEntity(productInfoEntity);
        analysisOutput.setPassengerFlowEntity(passengerFlowEntity);
        analysisOutput.setProductInfo(productInfo);
        analysisOutput.setShoppingInfoEntity(shoppingInfoEntity);
        analysisOutput.setOrderInfoEntity(orderInfoEntity);
        return analysisOutput;
    }

    /**
     * DashBoard数据一览取得
     *
     * @param storeId
     * @return
     */
    @Deprecated
    @Override
    public DashBoardInfoDto getDashBoardInfo(String storeId) {
//        MenuCountOutputDto menuCountData = new MenuCountOutputDto();
//		retList  memberInfoEntitys  outPutList
        DashBoardInfoDto dashBoardInfoDto = new DashBoardInfoDto();
//        //待审核会员信息获取
//        MemberInfoEntity entity = new MemberInfoEntity();
//        entity.setFlag(0);
//        entity.setUserAccount("");
//        entity.setUserName("");
//        entity.setStoreId(storeId);
//        List<MemberInfoEntity> memberInfoEntitys = memberDao.search(entity);
//        menuCountData.setAuthstrCount(memberInfoEntitys.size());
//        if (memberInfoEntitys.size() > 10) {
//            memberInfoEntitys = memberInfoEntitys.subList(0, 10);
//        }
//
////		List<BusinessTaskDto> orderTaskList = new ArrayList<BusinessTaskDto>();
////		List<String> orderIdList = new ArrayList<String>();
////		List<OrderInfoOutPutDto> retList = new ArrayList<OrderInfoOutPutDto>();
////
////		//0 流程Id 业务Id集合
////		List<ReturnOrderBussinessDto> returnIdList = new ArrayList<ReturnOrderBussinessDto>();
////		List<String> retIdList = new ArrayList<String>();
////		//1返回前台数据
////		List<ReturnOrderListOutputDto> outPutList = new ArrayList<ReturnOrderListOutputDto>();
////		try {
////			//待处理订单list获取
////			returnIdList = actReturnService.findPersonalReturnTask(SecurityUtils.getAutUserId(),0);
////
////			outPutList=	getOrderReturnInfo(returnIdList,retIdList);
////
////		//异常订单list获取
////		orderTaskList=actOrderService.findMyGroupTask(SecurityUtils.getAutUserId());
////		retList=getOrderInfo(orderTaskList,orderIdList);
////		} catch(Exception e) {
////			e.printStackTrace();
////		}
//        List<MstOrder> retList = getOrderInfo(storeId);
//        menuCountData.setPendingOrderCount(retList.size());
//        if (retList.size() > 10) {
//            retList = retList.subList(0, 10);
//        }
//        List<SalesReturnSearchOutputDto> outPutList = getOrderReturnInfo(storeId);
//        menuCountData.setSalesReturnCount(outPutList.size());
//        if (outPutList.size() > 10) {
//            outPutList = outPutList.subList(0, 10);
//        }
//
//        dashBoardInfoDto.setMemberInfoList(memberInfoEntitys);
//        dashBoardInfoDto.setOutPutList(outPutList);
//        dashBoardInfoDto.setRetList(retList);
//        List<ActivityFollowInfoDto> activityFollows = activityFollowService.getNowActivityFollow(storeId);
//        dashBoardInfoDto.setActList(activityFollows);
//        dashBoardInfoDto.setMenuCountData(menuCountData);
//
//        //今日销售额
//        Integer todayAmount = orderService.getTodayAmount(storeId);
//        dashBoardInfoDto.setTodayAmount(todayAmount);
//
//        //昨日销售额
//        Integer yesterdayAmount = orderService.getYesterdayAmount(storeId);
//        dashBoardInfoDto.setYesterdayAmount(yesterdayAmount);
//
//        //今週のセール情報
//        LocalDate today = LocalDate.now();
//        // 週の開始日を計算
//        LocalDate startOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
//
//        // 週の終了日を計算
//        LocalDate endOfWeek = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
//
//        WeekSalesStatusInputDto inputDto = new WeekSalesStatusInputDto();
//        inputDto.setStoreId(storeId);
//        inputDto.setWeekStartDate(String.valueOf(startOfWeek));
//        inputDto.setWeekEndDate(String.valueOf(endOfWeek));

        return dashBoardInfoDto;

    }

    /**
     * DashBoard数据一览取得 本周，本日，本月
     *
     * @param storeId
     * @return
     */
    @Override
    public DashBoardInfoDtoNew getDashBoardInfoNew(String storeId) {
        MenuCountOutputDto menuCountData = new MenuCountOutputDto();
//		retList  memberInfoEntitys  outPutList
        DashBoardInfoDtoNew dashBoardInfoDto = new DashBoardInfoDtoNew();
        //待审核会员信息获取
        MemberInfoEntity entity = new MemberInfoEntity();
        entity.setFlag(0);
        entity.setUserAccount("");
        entity.setUserName("");
        entity.setStoreId(storeId);
        List<MemberInfoEntity> memberInfoEntitys = memberDao.search(entity);
        menuCountData.setAuthstrCount(memberInfoEntitys.size());
        if (memberInfoEntitys.size() > 10) {
            memberInfoEntitys = memberInfoEntitys.subList(0, 10);
        }

        List<MstOrder> retList = getOrderInfo(storeId);
        menuCountData.setPendingOrderCount(retList.size());
        if (retList.size() > 10) {
            retList = retList.subList(0, 10);
        }
        List<SalesReturnSearchOutputDto> outPutList = getOrderReturnInfo(storeId);
        menuCountData.setSalesReturnCount(outPutList.size());
        if (outPutList.size() > 10) {
            outPutList = outPutList.subList(0, 10);
        }

        dashBoardInfoDto.setMemberInfoList(memberInfoEntitys);
        dashBoardInfoDto.setOutPutList(outPutList);
        dashBoardInfoDto.setRetList(retList);
        List<ActivityFollowInfoDto> activityFollows = activityFollowService.getNowActivityFollow(storeId);
        dashBoardInfoDto.setActList(activityFollows);
        dashBoardInfoDto.setMenuCountData(menuCountData);

        //今日销售额
        BigDecimal todayAmount = orderService.getTodayAmount(storeId);

        //今日退款额
        RefundAmountInputDto refundAmountInputDto = new RefundAmountInputDto();
        refundAmountInputDto.setStoreId(storeId);
        refundAmountInputDto.setOrderStatus(CommonConst.OrderStatus.STATUS_RETURN_COMPLETE.statusCode);
        refundAmountInputDto.setSalesReturnStatus(CommonConst.ReturnOrderStatus.STATUS_COMPLETE.statusCode);
        BigDecimal todayRefundAmount = orderService.getTodayRefundAmount(refundAmountInputDto);

        //今日实际销售额
        BigDecimal todayActualAmount = todayAmount.subtract(todayRefundAmount);
        dashBoardInfoDto.setTodayAmount(todayActualAmount);

        //昨日销售额
        BigDecimal yesterdayAmount = orderService.getYesterdayAmount(storeId);

        //昨日退款额
        BigDecimal yesterdayRefundAmount = orderService.getYesterdayRefundAmount(refundAmountInputDto);

        //昨日实际销售额
        BigDecimal yesterdayActualAmount = yesterdayAmount.subtract(yesterdayRefundAmount);

        dashBoardInfoDto.setYesterdayAmount(yesterdayActualAmount);

        //一周销售状况
        LocalDate today = LocalDate.now();

        WeekSalesStatusInputDto inputDto = new WeekSalesStatusInputDto();
        inputDto.setStoreId(storeId);

        // 计算日周月，往前推
        //	计算当前时间前一周的日期
        LocalDate startOfPreWeekDate = today.minusWeeks(1);
        LocalDate endOfNowWeek = today;

        // 计算一周内每天的退款金额
        refundAmountInputDto.setStartDate(String.valueOf(startOfPreWeekDate));
        refundAmountInputDto.setEndDate(String.valueOf(endOfNowWeek));
        List<WeekSalesAmountDto> refundAmountByDate = orderService.getWeekRefundAmount(refundAmountInputDto);

        // 计算一周内每天的销售额
        inputDto.setWeekStartDate(String.valueOf(startOfPreWeekDate));
        inputDto.setWeekEndDate(String.valueOf(endOfNowWeek));
        List<WeekSalesAmountDto> nowWeekSalesAmount = orderService.getNowWeekSaleAmount(inputDto);

        // 计算一周内每天的实际销售额
        nowWeekSalesAmount.forEach(salesAmount ->{
            refundAmountByDate.stream()
                    .filter(refundAmount->refundAmount.getDate().equals(salesAmount.getDate()))
                    .findFirst()
                    .ifPresent(refundAmount->{salesAmount.setAmount(salesAmount.getAmount().subtract(refundAmount.getAmount()));});
        });

        dashBoardInfoDto.setNowWeekSalesAmount(nowWeekSalesAmount);
        //info 对象
        WeekSalesOutputDto nowWeekSaleInfo = orderService.getSaleInfo(inputDto);
        dashBoardInfoDto.setNowWeekSalesInfo(nowWeekSaleInfo);


        //	计算当前时间前一个月的日期
        LocalDate startOfPreMonthDate = today.minusMonths(1);
        LocalDate endOfNowMonth = today;

        // 统计一个月的退款金额
        refundAmountInputDto.setStartDate(String.valueOf(startOfPreMonthDate));
        refundAmountInputDto.setEndDate(String.valueOf(endOfNowMonth));
        List<WeekSalesAmountDto> monthReturnAmount = orderService.getMonthRefundAmount(refundAmountInputDto);

        // 计算一个月内每5天的销售额
        inputDto.setWeekStartDate(String.valueOf(startOfPreMonthDate));
        inputDto.setWeekEndDate(String.valueOf(endOfNowMonth));
        List<WeekSalesAmountDto> monthNowWeekSalesAmount = orderService.getNowMonthSaleAmount(inputDto);
        // 计算一个月内每5天的实际销售额
        monthNowWeekSalesAmount.forEach(salesAmount ->{
            monthReturnAmount.stream()
                    .filter(refundAmount->refundAmount.getDate().equals(salesAmount.getDate()))
                    .findFirst()
                    .ifPresent(refundAmount->{salesAmount.setAmount(salesAmount.getAmount().subtract(refundAmount.getAmount()));});
        });

        dashBoardInfoDto.setNowMonthSalesAmount(monthNowWeekSalesAmount);

        //info 对象
        WeekSalesOutputDto nowMonthSaleInfo = orderService.getSaleInfo(inputDto);
        dashBoardInfoDto.setNowMonthSalesInfo(nowMonthSaleInfo);


        // 统计24小时内每4小时的退款金额
        List<WeekSalesAmountDto> hourReturnAmount = orderService.getHourRefundAmount(refundAmountInputDto);

        // 计算24小时内每4小时的销售额
        List<WeekSalesAmountDto> nowHoursSalesAmount = orderService.getNowHoursSaleAmount(inputDto);

        // 计算24小时内每4小时的实际销售额
        nowHoursSalesAmount.forEach(salesAmount ->{
            hourReturnAmount.stream()
                    .filter(refundAmount->refundAmount.getDate().equals(salesAmount.getDate()))
                    .findFirst()
                    .ifPresent(refundAmount->{salesAmount.setAmount(salesAmount.getAmount().subtract(refundAmount.getAmount()));});
        });

        dashBoardInfoDto.setNowHoursSalesAmount(nowHoursSalesAmount);

        //info 对象
        WeekSalesOutputDto nowHoursSaleInfo = orderService.getNowHoursSaleInfo(inputDto);

        dashBoardInfoDto.setNowHoursSalesInfo(nowHoursSaleInfo);

        return dashBoardInfoDto;

    }

    public List<MstOrder> getOrderInfo(String storeId) {
        List<MstOrder> orderInfoList = orderService.getWaitDealOrder(storeId);
        return orderInfoList;
    }

    public List<SalesReturnSearchOutputDto> getOrderReturnInfo(String storeId) {
        List<SalesReturnSearchOutputDto> retList = salesReturnOperationService.searchWaitDealSalesReturn(storeId);
        return retList;
    }

    /**
     * 店铺数据一览获取
     *
     * @param input
     * @return
     */
    public BigChartOutputDto getShopInfo(BigChartInput input) {
        BigChartOutputDto bigChartOutputDto = new BigChartOutputDto();

        //男女比例 TODO
        //支付信息 TODO
        //年龄 TODO
        AgeChartOutputDto ageChartOutputDto = new AgeChartOutputDto();
        ageChartOutputDto.setKey(new String[]{"50岁以上", "40-50岁", "30-40岁", "20-30岁", "20岁以下"});
        ageChartOutputDto.setValue(new int[]{5, 18, 46, 15, 11});
        bigChartOutputDto.setAgeChart(ageChartOutputDto);

        //总体报表
        MainChartOutputDto mainChartOutputDto = new MainChartOutputDto();
        ShoppingInfoInput shoppingInfoInput = new ShoppingInfoInput();
        shoppingInfoInput.setBeginTime(input.getStartTime());
        shoppingInfoInput.setEndTime(input.getEndTime());
        shoppingInfoInput.setStoreId(input.getStoreId());
        ShoppingInfoEntity shoppingInfoEntity = getShoppingInfo(shoppingInfoInput);

        if ("1".equals(input.getChartType())) {
            mainChartOutputDto.setToday(shoppingInfoEntity.getSalesVolume());
            mainChartOutputDto.setYesterday(shoppingInfoEntity.getLastSalesVolume());
        } else if ("2".equals(input.getChartType())) {
            mainChartOutputDto.setWeek(shoppingInfoEntity.getSalesVolume());
            mainChartOutputDto.setLastWeek(shoppingInfoEntity.getLastSalesVolume());
        } else if ("3".equals(input.getChartType())) {
            mainChartOutputDto.setMonth(shoppingInfoEntity.getSalesVolume());
            mainChartOutputDto.setLastMonth(shoppingInfoEntity.getLastSalesVolume());
        }
        bigChartOutputDto.setMainChart(mainChartOutputDto);


        //检索商品销量排行榜
        List<ProductInfoEntity> productInfoEntitys = analysisDao.getproductInfo();

        ProductChartOutputDto productChartOutputDto = new ProductChartOutputDto();
        List<SalesOutputDto> sales = new ArrayList<>();
        for (int i = 0; i < productInfoEntitys.size(); i++) {
            SalesOutputDto dto = new SalesOutputDto();
            dto.setValue(i);
            dto.setName(productInfoEntitys.get(i).getProductTitle());
            dto.setPicUrl(productInfoEntitys.get(i).getPicUrl());
            dto.setPrice(String.valueOf(productInfoEntitys.get(i).getProductSellCount()));
//            dto.setQuantity();
            dto.setSaleCount(productInfoEntitys.get(i).getProductSellCount());
            sales.add(dto);
        }
        productChartOutputDto.setSales(sales);

        //获取商品库存信息
        List<ProductInfo> productInfo = analysisDao.getProductStockInfo();
        List<StorageOutputDto> storage = new ArrayList<>();
        for (int i = 0; i < productInfo.size(); i++) {
            StorageOutputDto dto = new StorageOutputDto();
            dto.setValue(i);
            dto.setName(productInfo.get(i).getTitle());
            dto.setPicUrl(productInfo.get(i).getPicUrl());
            dto.setPrice(String.valueOf(productInfo.get(i).getPrice()));
            dto.setSaleCount(productInfo.get(i).getQuantity());
            storage.add(dto);
        }
        productChartOutputDto.setStorage(storage);
        bigChartOutputDto.setProductChart(productChartOutputDto);

        //进店人数获得
        ShoppingInfoEntity intoShopPeoCount = analysisDao.getIntoShopPeoCount(shoppingInfoInput);

        OrderInfoInput orderInfoInput = new OrderInfoInput();
        orderInfoInput.setBeginTime(input.getStartTime());
        orderInfoInput.setEndTime(input.getEndTime());
        orderInfoInput.setSearchType(Integer.parseInt(input.getChartType()) - 1);
        List<OrderInfoEntity> orderInfoEntity = getOrderInfo(orderInfoInput);

        String[] key = new String[orderInfoEntity.size()];
        int[] value = new int[orderInfoEntity.size()];
        int totalSale = 0;
        for (int i = 0; i < orderInfoEntity.size(); i++) {
            key[i] = orderInfoEntity.get(i).getDate();
            value[i] = orderInfoEntity.get(i).getOrderCount();
            totalSale += value[i];
        }
        //订单额度柱状图数据处理
        DailyOrderSalesChartOutputDto dailyOrderSalesChartOutputDto = new DailyOrderSalesChartOutputDto();
        dailyOrderSalesChartOutputDto.setKey(key);
        dailyOrderSalesChartOutputDto.setValue(value);
        dailyOrderSalesChartOutputDto.setTotalSale(totalSale);
        bigChartOutputDto.setDailyOrderSalesChart(dailyOrderSalesChartOutputDto);

        DailyGuestChartOutputDto dailyGuestChartOutputDto = new DailyGuestChartOutputDto();
        dailyGuestChartOutputDto.setTotal(intoShopPeoCount.getPeopleCount());//进店人数

        GuestChartOutputDto guestChartOutputDto = new GuestChartOutputDto();//客流量
        PassengerFlowInput passengerFlowInput = new PassengerFlowInput();
        passengerFlowInput.setBeginDay(input.getStartTime());
        passengerFlowInput.setEndDay(input.getEndTime());
        passengerFlowInput.setSearchType(Integer.parseInt(input.getChartType()) - 1);

        List<PassengerFlowEntity> passengerFlowEntitys = getPassengerFlow(passengerFlowInput);

        String[] key1 = new String[passengerFlowEntitys.size()];
        int[] value2 = new int[passengerFlowEntitys.size()];
        int totalSale2 = 0;
        if ("1".equals(input.getChartType())) {
            for (int i = 0; i < passengerFlowEntitys.size(); i++) {
                key1[i] = passengerFlowEntitys.get(i).getDate();
                value2[i] = passengerFlowEntitys.get(i).getPassengerFlow();
                totalSale2 += value2[i];
            }
            guestChartOutputDto.setKey(key1);
            guestChartOutputDto.setValue(value2);
            guestChartOutputDto.setToday(totalSale2);
            bigChartOutputDto.setGuestChart(guestChartOutputDto);
        } else {
            for (int i = 0; i < passengerFlowEntitys.size(); i++) {
                key1[i] = passengerFlowEntitys.get(i).getDate();
                value2[i] = passengerFlowEntitys.get(i).getPassengerFlow();
                totalSale2 += value2[i];
            }
            dailyGuestChartOutputDto.setKey(key1);
            dailyGuestChartOutputDto.setValue(value2);
            dailyGuestChartOutputDto.setTotal(totalSale2);
            bigChartOutputDto.setDailyGuestChart(dailyGuestChartOutputDto);
        }

        DailyOrderCountChartOutputDto dailyOrderCountChartOutputDto = new DailyOrderCountChartOutputDto();
        dailyOrderCountChartOutputDto.setTotalOrderCount(intoShopPeoCount.getOrderCount());//订单数量
        bigChartOutputDto.setDailyOrderCountChart(dailyOrderCountChartOutputDto);

        DailyBuyerCountChartOutputDto dailyBuyerCountChartOutputDto = new DailyBuyerCountChartOutputDto();
        dailyBuyerCountChartOutputDto.setTotalBuyerCount(intoShopPeoCount.getPeopleSellCount());//购买人次
        bigChartOutputDto.setDailyBuyerCountChart(dailyBuyerCountChartOutputDto);


        return bigChartOutputDto;
    }
}
