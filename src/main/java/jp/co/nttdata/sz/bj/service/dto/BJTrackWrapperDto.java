package jp.co.nttdata.sz.bj.service.dto;

import java.util.List;

import org.thanos.core.socket.server.BaseSocketMessage;

public class BJTrackWrapperDto extends BaseSocketMessage {
	
	private List<String> activityIds;

	public List<String> getActivityIds() {
		return activityIds;
	}

	public void setActivityIds(List<String> activityIds) {
		this.activityIds = activityIds;
	}

	
//	private BJTrackOutputDto trackData;


//	public BJTrackOutputDto getTrackData() {
//		return trackData;
//	}
//
//	public void setTrackData(BJTrackOutputDto trackData) {
//		this.trackData = trackData;
//	}
	
	
}
