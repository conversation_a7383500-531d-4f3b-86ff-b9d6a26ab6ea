
spring.datasource.com.jdbc-url=***************************************************************************************************
spring.datasource.com.username=root
spring.datasource.com.password=Sl123456

# spring.datasource.com.jdbc-url=jdbc:mysql://***********:3306/ePalette?useUnicode=true&characterEncoding=utf-8&serverTimezone=GMT%2B8
# spring.datasource.com.username=admin
# spring.datasource.com.password=Sl_123456
spring.datasource.com.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.com.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.com.comhikari.minimum-idle=5
spring.datasource.com.maximum-pool-size=15
spring.datasource.com.auto-commit=true
spring.datasource.com.idle-timeout=30000
spring.datasource.com.pool-name=sms_pool
spring.datasource.com.max-lifetime=1800000
spring.datasource.com.connection-timeout=30000
spring.datasource.com.connection-test-query=SELECT 1


#redis
spring.redis.database=13
#spring.redis.host=***********
spring.redis.host=localhost
spring.redis.port=6379
#spring.redis.password=1q2w3e4r


spring.main.allow-bean-definition-overriding=true

spring.http.encoding.force=true
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
server.tomcat.uri-encoding=UTF-8


spring.messages.basename=i18n/com/messages
spring.messages.encoding=UTF-8

mybatis.configuration.map-underscore-to-camel-case=true

spring.thymeleaf.cache=false

logging.config=classpath:config/logback.xml


#apacheï¿½ï¿½ï¿½ï¿½ï¿½Iï¿½ï¿½?ï¿½Hï¿½a
com.sz.apache.base.path=/var/www/html
#ï¿½p?ï¿½l??ï¿½ï¿½?ï¿½ÐÛï¿½ï¿½nï¿½ï¿½
com.sz.user.faceimg.base.path=/var/www/html/sms/member/face_register
#ï¿½ï¿½???ï¿½ÐÛï¿½ï¿½nï¿½ï¿½
com.sz.qrcode.save.path=/var/www/html/sms/auth/qrcode
#?ï¿½vï¿½l????ï¿½ÐÛï¿½ï¿½nï¿½ï¿½
com.sz.cs.face.auth.img.path=/var/www/html/sms/auth/face
#ï¿½ï¿½??ï¿½ï¿½ï¿½Ûï¿½ï¿½nï¿½ï¿½
com.sz.member.avatar.path=/var/www/html/sms/member/avatar
#ï¿½ï¿½??ï¿½ÐÛï¿½ï¿½nï¿½ï¿½
com.sz.member.feedback.path=/var/www/html/sms/member/feedback
#ï¿½ï¿½?ï¿½ï¿½??ï¿½ÐÛï¿½ï¿½nï¿½ï¿½
com.sz.sales.return.img.path=/var/www/html/sms/member/salesreturn
#?ï¿½ï¿½?ï¿½ÐÛï¿½ï¿½nï¿½ï¿½
com.sz.evaluation.img.path=/var/www/html/sms/member/evaluation
#ï¿½ï¿½???ï¿½ï¿½???ï¿½u
com.sz.qrcode.expiry.ms=300000
#apacheï¿½ï¿½ï¿½ï¿½ï¿½ï¿½?url
com.sz.apache.file.url=http://10.0.197.175:81

#ï¿½kï¿½ï¿½ï¿½l??ï¿½ï¿½url
com.sz.bj.face.regitser.url=http://58.214.16.141:9004/api/register
#ï¿½Xï¿½ï¿½?çæ¾url
com.sz.bj.track.get.url=http://58.214.16.141:28080/api/Track

#MQTT wx  \u5185\u90E8receive
#spring.mqtt.wx.username=admin
#spring.mqtt.wx.password=admin
#spring.mqtt.wx.url=tcp://10.0.197.174:1883
#spring.mqtt.wx.clientId=device_pc.receive-service-v001.client-v001-003
#spring.mqtt.wx.defaultTopic=sms_order
#spring.mqtt.wx.completionTimeout=1000
#spring.mqtt.wx.qos=1
#spring.mqtt.wx.connectTimeout=1000
#spring.mqtt.wx.keepAliveInterval=20

#acitiviti------------------------------------------
spring.activiti.database-schema-update=false
spring.activiti.db-history-used=true
spring.activiti.history-level=audit
spring.activiti.check-process-definitions=false

#spring boot admin config
#spring.boot.admin.client.url=http://10.0.197.187:8082
#management.endpoints.web.exposure.include=*
#spring.application.name=sms-web
#spring.boot.admin.client.instance.service-base-url=http://10.0.197.70:80


#socketï¿½zï¿½umessaageï¿½zï¿½u
com.sz.socket.message.mode=1

#rabbitMQ ï¿½zï¿½u
#?ï¿½ñ¼zï¿½u
spring.rabbitmq.listener.order.queue.name=sms-ios-queue
#?ï¿½ï¿½??ï¿½ï¿½ï¿½vï¿½ï¿½
spring.rabbitmq.listener.order.queue.durable=true
#ï¿½ï¿½?ï¿½í¼ï¿½zï¿½u
spring.rabbitmq.listener.order.exchange.name=amq.topic
#ï¿½ï¿½?ï¿½ï¿½??ï¿½ï¿½ï¿½vï¿½ï¿½
spring.rabbitmq.listener.order.exchange.durable=true
#ï¿½ï¿½?ï¿½ï¿½ï¿½ï¿½ï¿½
spring.rabbitmq.listener.order.exchange.type=topic
spring.rabbitmq.listener.order.exchange.ignoreDeclarationExceptions=true
#ï¿½Hï¿½R?ï¿½zï¿½u
spring.rabbitmq.listener.order.key=device_web.service-send.client-v0001
#ï¿½Hï¿½R?ï¿½zï¿½u-???ï¿½ï¿½
spring.rabbitmq.listener.shop.manager.key=shop.manager.notice.routingkey
#rabbitï¿½nï¿½ï¿½ï¿½zï¿½u
spring.rabbitmq.addresses=***********:5672
#ï¿½o?ï¿½p?ï¿½ï¿½
spring.rabbitmq.username=admin
#ï¿½o?ï¿½ï¿½?
spring.rabbitmq.password=admin
#â_ï¿½zï¿½u
spring.rabbitmq.virtual-host=/
#?ï¿½Úï¿½?
spring.rabbitmq.connection-timeout=15000
#?ï¿½zï¿½ï¿½???
spring.rabbitmq.publisher-returns=true
spring.rabbitmq.template.mandatory=true
#ï¿½ï¿½??ï¿½ï¿½?ï¿½ï¿½??
spring.rabbitmq.queue.delayms=180000


redis.data.save.days=7
#ï¿½Xï¿½ï¿½ï¿½Åï¿½lï¿½ï¿½
store.into.person.limit=5
ncit.cs.self=https://smartdevice.api.troncell.com/api/services/app/SensingDevice/GetShelfInfosInStore
ncit.cs.product=https://product.api.troncell.com/api/services/app/SensingDevice/GetProducts
ncit.cs.weight=https://smartdevice.api.troncell.com/api/services/app/SensingDevice/GetCargoRoadsWeight
ncit.cs.subKey=33682db75cff447db210e186561423a4

#çæ§çç«¯ç¹
management.endpoints.web.exposure.include=*
#åºç¨ç¨åºåç§°ï¼å¨prometheus ä¸ä¼æ¾ç¤º
management.metrics.tags.application=${spring.application.name}
#tomcat ææ éè¦å¼å¯
server.tomcat.mbeanregistry.enabled=true
