server.port=8080
server.servlet.context-path=/sms
server.servlet.session.timeout=259200
spring.servlet.multipart.maxFileSize=10MB
spring.servlet.multipart.maxRequestSize=20MB

#spring.profiles.active=@profiles.active@
spring.profiles.active=dev
spring.main.allow-bean-definition-overriding=true

spring.http.encoding.force=true
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
server.tomcat.uri-encoding=UTF-8


spring.messages.basename=i18n/com/messages
spring.messages.encoding=UTF-8

mybatis.configuration.map-underscore-to-camel-case=true

spring.thymeleaf.cache=false

logging.config=classpath:config/logback.xml

#å¯ç¨shutdown
endpoints.shutdown.enabled=true
#ç¦ç¨å¯ç éªè¯
endpoints.shutdown.sensitive=false



redis.data.save.days=7
#åºåæå¤§äººæ°
store.into.person.limit=5
ncit.cs.self=https://smartdevice.api.troncell.com/api/services/app/SensingDevice/GetShelfInfosInStore
ncit.cs.product=https://product.api.troncell.com/api/services/app/SensingDevice/GetProducts
ncit.cs.weight=https://smartdevice.api.troncell.com/api/services/app/SensingDevice/GetCargoRoadsWeight
ncit.cs.subKey=33682db75cff447db210e186561423a4

#çæ§çç«¯ç¹
management.endpoints.web.exposure.include=*
#åºç¨ç¨åºåç§°ï¼å¨prometheus ä¸ä¼æ¾ç¤º
management.metrics.tags.application=${spring.application.name}
#tomcat ææ éè¦å¼å¯
server.tomcat.mbeanregistry.enabled=true

##æä»¶æå¡éç½®
#è®¿é®å°å
file.domain=http://10.0.198.70:31410/sms/file/download
#å­å¨ä½ç½®
file.path=/static/upload
#åç¼
file.prefix=/static/upload
