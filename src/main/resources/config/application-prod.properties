

#spring.datasource.com.jdbc-url=**************************************************************************************************************
spring.datasource.com.jdbc-url=********************************************************************************************************************
spring.datasource.com.username=wms
spring.datasource.com.password=1Qaz2wsx3edc
spring.datasource.com.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.com.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.com.comhikari.minimum-idle=5
spring.datasource.com.maximum-pool-size=15
spring.datasource.com.auto-commit=true
spring.datasource.com.idle-timeout=30000
spring.datasource.com.pool-name=sms_pool
spring.datasource.com.max-lifetime=1800000
spring.datasource.com.connection-timeout=30000
spring.datasource.com.connection-test-query=SELECT 1

#redis
spring.redis.database=1
spring.redis.host=************
spring.redis.port=6379


spring.main.allow-bean-definition-overriding=true

spring.http.encoding.force=true
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
server.tomcat.uri-encoding=UTF-8


spring.messages.basename=i18n/com/messages
spring.messages.encoding=UTF-8

mybatis.configuration.map-underscore-to-camel-case=true

spring.thymeleaf.cache=false

logging.config=classpath:config/logback.xml

#å¯ç¨shutdown
endpoints.shutdown.enabled=true
#ç¦ç¨å¯ç éªè¯
endpoints.shutdown.sensitive=false



#apacheæä»¶çåºç¡è·¯å¾
com.sz.apache.base.path=/var/www/html
#ç¨æ·äººè¸å½å¥å¾çä¿å­å°å
com.sz.user.faceimg.base.path=/var/www/html/smsfile/member/face_register
#äºç»´ç å¾çä¿å­å°å
com.sz.qrcode.save.path=/var/www/html/smsfile/auth/qrcode
#åæäººè¸è®¤è¯å¾çä¿å­å°å
com.sz.cs.face.auth.img.path=/var/www/html/smsfile/auth/face
#ä¼åå¤´åä¿å­å°å
com.sz.member.avatar.path=/var/www/html/smsfile/member/avatar
#åé¦å¾çä¿å­å°å
com.sz.member.feedback.path=/var/www/html/smsfile/member/feedback
#éè´§ç¸å³å¾çä¿å­å°å
com.sz.sales.return.img.path=/var/www/html/smsfile/member/salesreturn
#è¯ä»·å¾çä¿å­å°å
com.sz.evaluation.img.path=/var/www/html/smsfile/member/evaluation
#äºç»´ç è¿ææ¶é´é´é
com.sz.qrcode.expiry.ms=300000
#æä»¶åºç¡url  å¤é¨è®¿é®ç¨
com.sz.apache.file.url=http://54.238.13.140

#åäº¬äººè¸å½å¥url
com.sz.bj.face.regitser.url=http://58.214.16.141:9004/api/register
#åºåè½¨è¿¹åå¾url
com.sz.bj.track.get.url=http://172.31.2.7:9140/api/Track


#acitiviti------------------------------------------
#set true to init DB
spring.activiti.database-schema-update=false
spring.activiti.db-history-used=true 
spring.activiti.history-level=audit 
spring.activiti.check-process-definitions=false

#socketéç½®messaageéç½®
com.sz.socket.message.mode=1


#rabbitMQ éç½®
#éååéç½®
spring.rabbitmq.listener.order.queue.name=sms-ios-queue
#éåå¼å¯æä¹å
spring.rabbitmq.listener.order.queue.durable=true
#äº¤æ¢å¨åéç½®
spring.rabbitmq.listener.order.exchange.name=amq.topic
#äº¤æ¢å¨å¼å¯æä¹å
spring.rabbitmq.listener.order.exchange.durable=true
#äº¤æ¢å¨æ¹å¼
spring.rabbitmq.listener.order.exchange.type=topic
spring.rabbitmq.listener.order.exchange.ignoreDeclarationExceptions=true
#è·¯ç±é®éç½®
spring.rabbitmq.listener.order.key=device_web.service-send.client-v0001
#è·¯ç±é®éç½®-ç»è®¡éå
spring.rabbitmq.listener.shop.manager.key=shop.manager.notice.routingkey
#rabbitå°åéç½®
spring.rabbitmq.addresses=************:5672
#ç»å½ç¨æ·å
spring.rabbitmq.username=admin
#ç»å½å¯ç 
spring.rabbitmq.password=1q2w3e4r
#ç«ç¹éç½®
spring.rabbitmq.virtual-host=/
#è¿æ¥è¶æ¶
spring.rabbitmq.connection-timeout=15000
#åå¸åé¦å¼å¯
spring.rabbitmq.publisher-returns=true
spring.rabbitmq.template.mandatory=true
#å»¶è¿éåè®¾å®æ¶é´
spring.rabbitmq.queue.delayms=180000


redis.data.save.days=7
#åºåæå¤§äººæ°
store.into.person.limit=5
ncit.cs.self=https://smartdevice.api.troncell.com/api/services/app/SensingDevice/GetShelfInfosInStore
ncit.cs.product=https://product.api.troncell.com/api/services/app/SensingDevice/GetProducts
ncit.cs.weight=https://smartdevice.api.troncell.com/api/services/app/SensingDevice/GetCargoRoadsWeight
ncit.cs.subKey=33682db75cff447db210e186561423a4
