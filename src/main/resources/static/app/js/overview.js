/**
 * Created by Astro on 2019/3/21.
 */
var detailTableA = null;
var DATATABLE_DOM = '<"H">t<"F"ip>';

function showTypeData() {
    if(!$("#detailTableA").hasClass("dataTable")){
        $("#detailTableA").addClass("dataTable");
    }
    var dataUrl = "/v1/content/condition" ;
    if (isEmpty(detailTableA)) {
        detailTableA = $("#detailTableA").DataTable({
            "dom": DATATABLE_DOM,
            "autoWidth": true,
            "scrollCollapse": true,
            "info": false,
            "filter": false,
            "ordering": true,
            "paging": true,
            "bProcessing": true,
            "order": [[0, "desc"]],
            "ajax": {
                "url": dataUrl,
                "type":"post",
                "contentType": 'application/json',
                "data": function () {
                    // var data = eval(result);
                    // return data.output_data;
                var data= getSearchData();
                return JSON.stringify(data);
                }
                // ,
                // "dataSrc": function (result) {
                //     var data = eval(result);
                //     return data.output_data;
                // }
            },
            "fnInitComplete": function () {
                this.fnAdjustColumnSizing(true);
            },
            "columns": [{
                "title": "订单编号",
                "data": "orderName"
            }, {
                "title": "类别",
                "data": "typeAName"
            }, {
                "title": "型号",
                "data": "typeBName"
            }, {
                "title": "颜色",
                "data": "color"
            }, {
                "title": "数量(匹)",
                "data": "count"
            }, {
                "title": "重量(KG)",
                "data": "weight"
            }, {
                "title": "生产商",
                "data": "companyName"
            }, {
                "title": "订单说明",
                "data": "orderComment"
            }, {
                "title": "操作类型",
                "data": "tradeType",
                "render": function (data, type, full, meta) {
                    if(full.tradeType == 1){
                        return full.tradeName;
                    }else{
                        return '<span style="color: red">'+full.tradeName+'</span>';
                    }

                }
            }, {
                "title": "创建时间",
                "data": "createTime",
            }, {
                "title": "备注",
                "data": "comment"
            }]
        });
    } else {
        detailTableA.ajax.url(dataUrl).load();
    }
}