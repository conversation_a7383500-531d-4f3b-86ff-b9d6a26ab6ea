<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.SalesReturnDao">

	<select id="searchSalesReturn"
		parameterType="jp.co.nttdata.sz.web.service.salesreturn.dto.SalesReturnSearchInputDto"
		resultType="jp.co.nttdata.sz.web.service.salesreturn.dto.SalesReturnSearchOutputDto"> SELECT
		t1.sales_return_id AS returnId, t1.user_id AS userId, t3.user_account AS userAccount,
		t1.order_id AS orderId, t1.tags AS tags, t1.images AS images, t1.sales_retrun_status AS
		returnStatus, t1.sales_return_operation_user AS operationUser, t4.user_account
		operationAccount, t1.sales_return_create_time AS createTime, t1.sales_return_end_time AS
		endTime, t1.reason AS reason, t2.amount AS amount FROM mst_sales_return AS t1 LEFT JOIN
		mst_order AS t2 ON t1.order_id=t2.order_id LEFT JOIN sys_user AS t3 ON t1.user_id=t3.user_id
		LEFT JOIN sys_user AS t4 ON t1.sales_return_operation_user=t4.user_id WHERE 1=1 <if
			test="orderId!=null and orderId!=''"> AND t1.order_id like
		concat(concat('%',#{orderId}),'%') </if>
		<if test="returnStatus!=null and returnStatus!=''">
		AND t1.sales_retrun_status like concat(concat('%',#{returnStatus}),'%') </if>
		<if
			test="startTime != null and startTime !=''"> AND
		t1.sales_return_create_time>#{startTime} </if>
		<if test="endTime != null and endTime !=''">
		AND t1.sales_return_create_time &lt; #{endTime} </if> ORDER BY createTime DESC </select>

	<select id="getWaitDealSalesReturn"
		resultType="jp.co.nttdata.sz.web.service.salesreturn.dto.SalesReturnSearchOutputDto"> SELECT
		t1.order_id AS orderId, t1.user_id AS userId, t2.settlement_type AS settlementType,
		t1.sales_return_id AS returnId, t1.sales_return_create_time AS createTime, t3.user_account
		AS userAccount, t1.reason AS reason, t1.return_amount AS amount, t1.tags AS tags, t1.images
		AS images, t1.sales_retrun_status AS returnStatus, t1.sales_return_operation_user AS
		operationUser, t4.user_account as operationAccount, t1.sales_return_end_time AS endTime FROM
		mst_sales_return AS t1 LEFT JOIN mst_order AS t2 ON t1.order_id = t2.order_id LEFT JOIN
		sys_user AS t3 ON t1.user_id = t3.user_id LEFT JOIN sys_user AS t4 ON
		t1.sales_return_operation_user = t4.user_id WHERE sales_retrun_status = '0' <if
			test="storeId !=null and storeId !=''"> AND t2.consumer_store_id = #{storeId} </if>
		ORDER BY createTime DESC </select>

	<select id="getSalesReturnInfoById" parameterType="java.lang.String"
		resultType="jp.co.nttdata.sz.web.service.salesreturn.dto.SalesReturnSearchOutputDto"> SELECT
		t1.sales_return_id AS returnId, t1.user_id AS userId, t3.user_account AS userAccount,
		t1.order_id AS orderId, t1.sales_retrun_status AS returnStatus,
		t1.sales_return_operation_user AS operationUser, t4.user_account operationAccount,
		t1.sales_return_create_time AS createTime, t1.sales_return_end_time AS endTime, t1.reason AS
		reason, t2.amount AS amount, t1.return_amount AS returnAmount FROM mst_sales_return AS t1
		LEFT JOIN mst_order AS t2 ON t1.order_id=t2.order_id LEFT JOIN sys_user AS t3 ON
		t1.user_id=t3.user_id LEFT JOIN sys_user AS t4 ON t1.sales_return_operation_user=t4.user_id
		WHERE t1.sales_return_id=#{returnId} </select>

	<select id="getSalesReturnInfoByIds"
		resultType="jp.co.nttdata.sz.web.service.salesreturn.dto.ReturnInfoOutputDto"> SELECT
		t1.sales_return_id AS returnId, t1.user_id AS userId, t3.user_account AS userAccount,
		t1.order_id AS orderId, t1.tags AS tags, t1.images AS images, t1.sales_retrun_status AS
		returnStatus, t1.sales_return_operation_user AS operationUser, t4.user_account
		operationAccount, t1.sales_return_create_time AS createTime, t1.sales_return_end_time AS
		endTime, t1.reason AS reason, t2.amount AS amount FROM mst_sales_return AS t1 LEFT JOIN
		mst_order AS t2 ON t1.order_id=t2.order_id LEFT JOIN sys_user AS t3 ON t1.user_id=t3.user_id
		LEFT JOIN sys_user AS t4 ON t1.sales_return_operation_user=t4.user_id <if
			test="returnIds!=null">
			<foreach collection="returnIds"
				open="WHERE t1.sales_return_id in (" close=")" item="returnId"
				separator=","> #{returnId} </foreach>
		</if>

	</select>

	<select id="getReturnItemById" parameterType="java.lang.String"
		resultType="jp.co.nttdata.sz.web.entity.SalesReturnItemEntity"> SELECT sales_return_id AS
		salesReturnId, product_id AS product_id, t1.product_title AS productTitle, t2.pic_url AS
		picUrl, t1.price AS pirce, t1.quantity AS quantity, status AS status, reason AS reason,
		reason_tags AS reasonTags, reason_pic AS reasonPic FROM sales_return_items AS t1 LEFT JOIN
		mst_products AS t2 ON t1.product_id = t2.id WHERE sales_return_id = #{returnId} </select>

	<select id="getSalesReturnItemById" parameterType="java.lang.String"
		resultType="jp.co.nttdata.sz.web.entity.SalesReturnItemEntity"> SELECT sales_return_id AS
		salesReturnId, product_id AS productId, product_title AS productTitle, price AS pirce,status AS status,
		quantity AS quantity,return_quantity AS returnQuantity, return_amount AS returnAmount FROM sales_return_items WHERE
		sales_return_id = #{returnId}</select>


	<!-- 根据订单ID查询申请退货订单明细 -->
	<select id="getOrderReturnItemByOrderId"
		resultType="jp.co.nttdata.sz.web.entity.SalesReturnItemEntity"> SELECT sales_return_id AS
		salesReturnId, product_id AS productId, product_title AS productTitle, price AS pirce,
		quantity AS quantity,return_quantity AS returnQuantity, reason_pic as picUrl, status As status, return_amount AS returnAmount
		FROM sales_return_items WHERE sales_return_id = #{orderId} </select>

	<insert id="insertSalesReturn"
		parameterType="jp.co.nttdata.sz.web.entity.SalesReturnInfoEntity"> INSERT INTO
		mst_sales_return (sales_return_id, user_id, order_id, sales_retrun_status,
		sales_return_operation_user, sales_return_create_time, sales_return_end_time, reason,
		return_amount) VALUES
		(#{returnId,jdbcType=VARCHAR},#{userId,jdbcType=VARCHAR},#{orderId,jdbcType=VARCHAR}
		,#{returnStatus,jdbcType=VARCHAR},#{operationUser,jdbcType=VARCHAR},CURRENT_TIMESTAMP,
		CURRENT_TIMESTAMP,#{reason,jdbcType=VARCHAR},#{returnAmount,jdbcType=VARCHAR}) </insert>

	<insert id="insertSalesReturnItem"
		parameterType="jp.co.nttdata.sz.web.entity.SalesReturnItemEntity"> INSERT INTO
		sales_return_items
		(sales_return_id,product_id,product_title,price,return_amount,quantity,status,reason,reason_pic)
		VALUES
		(#{salesReturnId},#{productId},#{productTitle},#{pirce},#{returnAmount},#{quantity},#{status},#{reason},#{reasonPic})

		</insert>

	<insert id="insertWeixinReturnItem"
		parameterType="jp.co.nttdata.sz.web.entity.WeixinReturnItemEntity"> INSERT INTO
		sales_return_items
		(sales_return_id,product_id,product_title,price,quantity,status,reason,reason_pic,reason_tags)
		VALUES
		(#{salesReturnId},#{productId},#{productTitle},#{pirce},#{quantity},#{status},#{reason},#{reasonPic},#{reasonTags})
		</insert>
	<update id="updateReturnStatus"
		parameterType="jp.co.nttdata.sz.web.entity.SalesReturnInfoEntity"> UPDATE mst_sales_return
		SET sales_retrun_status =#{returnStatus} WHERE sales_return_id=#{returnId} </update>

	<update id="updateSalesReturnItem"
		parameterType="jp.co.nttdata.sz.web.entity.SalesReturnItemEntity"> UPDATE sales_return_items
		SET status = #{status}, quantity = #{quantity},return_quantity = #{returnQuantity}, returnContent = #{returnContent} WHERE
		sales_return_id = #{salesReturnId} AND product_id = #{productId} </update>

	<update id="updateSalesReturnStatus"
		parameterType="jp.co.nttdata.sz.web.entity.SalesReturnInfoEntity"> UPDATE mst_sales_return
		SET sales_retrun_status = #{returnStatus}, sales_return_operation_user = #{userId},
		returnContent = #{returnContent}, sales_return_end_time = CURRENT_TIMESTAMP, return_amount =
		#{returnAmount} WHERE sales_return_id = #{returnId} </update>

	<select id="getSalesReturnInfoByIdsAndCondtion"
		resultType="jp.co.nttdata.sz.web.service.salesreturn.dto.ReturnInfoOutputDto"> SELECT
		t1.sales_return_id AS returnId, t1.user_id AS userId, t3.user_account AS userAccount,
		t2.settlement_type as settlementType, t1.order_id AS orderId, t1.tags AS tags, t1.images AS
		images, t1.sales_retrun_status AS returnStatus, t1.sales_return_operation_user AS
		operationUser, t4.user_account operationAccount, t1.sales_return_create_time AS createTime,
		t1.sales_return_end_time AS endTime, t1.reason AS reason, t2.amount AS amount FROM
		mst_sales_return AS t1 LEFT JOIN mst_order AS t2 ON t1.order_id=t2.order_id LEFT JOIN
		sys_user AS t3 ON t1.user_id=t3.user_id LEFT JOIN sys_user AS t4 ON
		t1.sales_return_operation_user=t4.user_id WHERE 1=1 <if
			test="inputDto.orderId !=null and inputDto.orderId !=''"> AND t1.order_id like
		concat(concat('%',#{inputDto.orderId}),'%') </if>
		<if
			test="inputDto.beginTime !=null and inputDto.beginTime !=''"> AND
		t1.sales_return_create_time >= concat(#{inputDto.beginTime},' 00:00:00') </if>
		<if
			test="inputDto.endTime !=null and inputDto.endTime !=''"> AND
		t1.sales_return_create_time &lt;= concat(#{inputDto.endTime},' 23:59:59') </if>
		<if
			test="inputDto.userAccount !=null and inputDto.userAccount !=''"> AND t3.user_account
		like concat(concat('%',#{inputDto.userAccount}),'%') </if>
		<if test="returnIds!=null">
			<foreach collection="returnIds"
				open="AND t1.sales_return_id in (" close=")" item="returnId"
				separator=","> #{returnId} </foreach>
		</if>

	</select>

	<select id="searchSalesReturnByCondition"
		parameterType="jp.co.nttdata.sz.web.service.activiti.dto.ReturnSearchInputDto"
		resultType="jp.co.nttdata.sz.web.service.salesreturn.dto.ReturnInfoOutputDto"> SELECT
		t1.sales_return_id AS returnId, t1.user_id AS userId, t3.user_account AS userAccount,
		t2.settlement_type as settlementType, t1.order_id AS orderId, t1.tags AS tags, t1.images AS
		images, t1.sales_retrun_status AS returnStatus, t2.order_status AS orderStatus,
		t1.sales_return_operation_user AS operationUser, t4.user_account operationAccount,
		t1.sales_return_create_time AS createTime, t1.sales_return_end_time AS endTime, t1.reason AS
		reason, t1.return_amount AS amount FROM mst_sales_return AS t1 LEFT JOIN mst_order AS t2 ON
		t1.order_id=t2.order_id LEFT JOIN sys_user AS t3 ON t1.user_id=t3.user_id LEFT JOIN sys_user
		AS t4 ON t1.sales_return_operation_user=t4.user_id WHERE 1=1 <if
			test="orderId !=null and orderId !=''"> AND t1.order_id like
		concat(concat('%',#{orderId}),'%') </if>
		<if test="beginTime !=null and beginTime !=''"> AND
		t1.sales_return_create_time >= concat(#{beginTime},' 00:00:00') </if>
		<if
			test="endTime !=null and endTime !=''"> AND t1.sales_return_create_time &lt;=
		concat(#{endTime},' 23:59:59') </if>
		<if test="userAccount !=null and userAccount !=''"> AND
		t3.user_account like concat(concat('%',#{userAccount}),'%') </if>
		<if
			test="returnStatus !=null and returnStatus !=''"> AND t1.sales_retrun_status
		=#{returnStatus} </if>
		<if test="orderStatus !=null and orderStatus !=''"> AND
		t2.order_status =#{orderStatus} </if>
		<if test="storeId !=null and storeId !=''"> AND
		t2.consumer_store_id =#{storeId} </if> ORDER BY t1.sales_return_create_time DESC </select>
</mapper>
