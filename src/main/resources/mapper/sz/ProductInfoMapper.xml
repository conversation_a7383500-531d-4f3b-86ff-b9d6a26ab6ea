<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.dao.ProductInfoDao">
    <resultMap id="BaseResultMap"
               type="jp.co.nttdata.sz.web.entity.ProductInfo">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="item_id" jdbcType="VARCHAR" property="itemId"/>
        <result column="quantity" jdbcType="INTEGER"
                property="quantity"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="organization_unit_id" jdbcType="INTEGER"
                property="organizationUnitId"/>
        <result column="sub_title" jdbcType="VARCHAR"
                property="subTitle"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="prom_price" jdbcType="DECIMAL"
                property="promPrice"/>
        <result column="barcode" jdbcType="VARCHAR" property="barcode"/>
        <result column="sales_volume" jdbcType="INTEGER"
                property="salesVolume"/>
        <result column="keywords" jdbcType="VARCHAR"
                property="keywords"/>
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl"/>
        <result column="like_count" jdbcType="INTEGER"
                property="likeCount"/>
        <result column="description" jdbcType="VARCHAR"
                property="description"/>
        <result column="is_from_brand" jdbcType="BIT"
                property="isFromBrand"/>
        <result column="seller_id" jdbcType="VARCHAR"
                property="sellerId"/>
        <result column="outer_id" jdbcType="VARCHAR" property="outerId"/>
        <result column="from_type" jdbcType="VARCHAR"
                property="fromType"/>
        <result column="has_real_skus" jdbcType="BIT"
                property="hasRealSkus"/>
        <result column="age_scope" jdbcType="VARCHAR"
                property="ageScope"/>
        <result column="gender" jdbcType="VARCHAR" property="gender"/>
        <result column="rfid_code" jdbcType="VARCHAR"
                property="rfidCode"/>
        <result column="price2" jdbcType="VARCHAR" property="price2"/>
        <result column="group_qr_code_info" jdbcType="VARCHAR"
                property="groupQrCodeInfo"/>
        <result column="order_number" jdbcType="INTEGER"
                property="orderNumber"/>
        <result column="brand_id" jdbcType="INTEGER" property="brandId"/>
        <result column="language" jdbcType="VARCHAR"
                property="language"/>
        <result column="region" jdbcType="VARCHAR" property="region"/>
        <result column="point_readeem_type" jdbcType="INTEGER"
                property="pointReadeemType"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , item_id, quantity, title, organization_unit_id, sub_title, price,
		prom_price,
		barcode, sales_volume, keywords, pic_url, like_count, description,
		is_from_brand,
		seller_id, outer_id, from_type, has_real_skus, age_scope, gender, rfid_code,
		price2,
		group_qr_code_info, order_number, brand_id, language, region, point_readeem_type
    </sql>
    <select id="selectByPrimaryKey"
            parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mst_products
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey"
            parameterType="java.lang.Integer">
        delete
        from mst_products
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert"
            parameterType="jp.co.nttdata.sz.web.entity.ProductInfo">
        insert into mst_products (id, item_id, quantity,
                                  title, organization_unit_id, sub_title,
                                  price, prom_price, barcode,
                                  sales_volume, keywords, pic_url,
                                  like_count, description, is_from_brand,
                                  seller_id, outer_id, from_type,
                                  has_real_skus, age_scope, gender,
                                  rfid_code, price2, group_qr_code_info,
                                  order_number, brand_id, language,
                                  region, point_readeem_type)
        values (#{id,jdbcType=INTEGER}, #{itemId,jdbcType=VARCHAR},
                #{quantity,jdbcType=INTEGER},
                #{title,jdbcType=VARCHAR}, #{organizationUnitId,jdbcType=INTEGER}, #{subTitle,jdbcType=VARCHAR},
                #{price,jdbcType=DECIMAL}, #{promPrice,jdbcType=DECIMAL},
                #{barcode,jdbcType=VARCHAR},
                #{salesVolume,jdbcType=INTEGER}, #{keywords,jdbcType=VARCHAR}, #{picUrl,jdbcType=VARCHAR},
                #{likeCount,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR},
                #{isFromBrand,jdbcType=BIT},
                #{sellerId,jdbcType=VARCHAR}, #{outerId,jdbcType=VARCHAR}, #{fromType,jdbcType=VARCHAR},
                #{hasRealSkus,jdbcType=BIT}, #{ageScope,jdbcType=VARCHAR},
                #{gender,jdbcType=VARCHAR},
                #{rfidCode,jdbcType=VARCHAR}, #{price2,jdbcType=VARCHAR}, #{groupQrCodeInfo,jdbcType=VARCHAR},
                #{orderNumber,jdbcType=INTEGER}, #{brandId,jdbcType=INTEGER},
                #{language,jdbcType=VARCHAR},
                #{region,jdbcType=VARCHAR}, #{pointReadeemType,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective"
            parameterType="jp.co.nttdata.sz.web.entity.ProductInfo">
        insert into mst_products
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="organizationUnitId != null">
                organization_unit_id,
            </if>
            <if test="subTitle != null">
                sub_title,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="promPrice != null">
                prom_price,
            </if>
            <if test="barcode != null">
                barcode,
            </if>
            <if test="salesVolume != null">
                sales_volume,
            </if>
            <if test="keywords != null">
                keywords,
            </if>
            <if test="picUrl != null">
                pic_url,
            </if>
            <if test="likeCount != null">
                like_count,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="isFromBrand != null">
                is_from_brand,
            </if>
            <if test="sellerId != null">
                seller_id,
            </if>
            <if test="outerId != null">
                outer_id,
            </if>
            <if test="fromType != null">
                from_type,
            </if>
            <if test="hasRealSkus != null">
                has_real_skus,
            </if>
            <if test="ageScope != null">
                age_scope,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="rfidCode != null">
                rfid_code,
            </if>
            <if test="price2 != null">
                price2,
            </if>
            <if test="groupQrCodeInfo != null">
                group_qr_code_info,
            </if>
            <if test="orderNumber != null">
                order_number,
            </if>
            <if test="brandId != null">
                brand_id,
            </if>
            <if test="language != null">
                language,
            </if>
            <if test="region != null">
                region,
            </if>
            <if test="pointReadeemType != null">
                point_readeem_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="itemId != null">
                #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="organizationUnitId != null">
                #{organizationUnitId,jdbcType=INTEGER},
            </if>
            <if test="subTitle != null">
                #{subTitle,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="promPrice != null">
                #{promPrice,jdbcType=DECIMAL},
            </if>
            <if test="barcode != null">
                #{barcode,jdbcType=VARCHAR},
            </if>
            <if test="salesVolume != null">
                #{salesVolume,jdbcType=INTEGER},
            </if>
            <if test="keywords != null">
                #{keywords,jdbcType=VARCHAR},
            </if>
            <if test="picUrl != null">
                #{picUrl,jdbcType=VARCHAR},
            </if>
            <if test="likeCount != null">
                #{likeCount,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="isFromBrand != null">
                #{isFromBrand,jdbcType=BIT},
            </if>
            <if test="sellerId != null">
                #{sellerId,jdbcType=VARCHAR},
            </if>
            <if test="outerId != null">
                #{outerId,jdbcType=VARCHAR},
            </if>
            <if test="fromType != null">
                #{fromType,jdbcType=VARCHAR},
            </if>
            <if test="hasRealSkus != null">
                #{hasRealSkus,jdbcType=BIT},
            </if>
            <if test="ageScope != null">
                #{ageScope,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=VARCHAR},
            </if>
            <if test="rfidCode != null">
                #{rfidCode,jdbcType=VARCHAR},
            </if>
            <if test="price2 != null">
                #{price2,jdbcType=VARCHAR},
            </if>
            <if test="groupQrCodeInfo != null">
                #{groupQrCodeInfo,jdbcType=VARCHAR},
            </if>
            <if test="orderNumber != null">
                #{orderNumber,jdbcType=INTEGER},
            </if>
            <if test="brandId != null">
                #{brandId,jdbcType=INTEGER},
            </if>
            <if test="language != null">
                #{language,jdbcType=VARCHAR},
            </if>
            <if test="region != null">
                #{region,jdbcType=VARCHAR},
            </if>
            <if test="pointReadeemType != null">
                #{pointReadeemType,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="jp.co.nttdata.sz.web.entity.ProductInfo">
        update mst_products
        <set>
            <if test="itemId != null">
                item_id = #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="organizationUnitId != null">
                organization_unit_id = #{organizationUnitId,jdbcType=INTEGER},
            </if>
            <if test="subTitle != null">
                sub_title = #{subTitle,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="promPrice != null">
                prom_price = #{promPrice,jdbcType=DECIMAL},
            </if>
            <if test="barcode != null">
                barcode = #{barcode,jdbcType=VARCHAR},
            </if>
            <if test="salesVolume != null">
                sales_volume = #{salesVolume,jdbcType=INTEGER},
            </if>
            <if test="keywords != null">
                keywords = #{keywords,jdbcType=VARCHAR},
            </if>
            <if test="picUrl != null">
                pic_url = #{picUrl,jdbcType=VARCHAR},
            </if>
            <if test="likeCount != null">
                like_count = #{likeCount,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="isFromBrand != null">
                is_from_brand = #{isFromBrand,jdbcType=BIT},
            </if>
            <if test="sellerId != null">
                seller_id = #{sellerId,jdbcType=VARCHAR},
            </if>
            <if test="outerId != null">
                outer_id = #{outerId,jdbcType=VARCHAR},
            </if>
            <if test="fromType != null">
                from_type = #{fromType,jdbcType=VARCHAR},
            </if>
            <if test="hasRealSkus != null">
                has_real_skus = #{hasRealSkus,jdbcType=BIT},
            </if>
            <if test="ageScope != null">
                age_scope = #{ageScope,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=VARCHAR},
            </if>
            <if test="rfidCode != null">
                rfid_code = #{rfidCode,jdbcType=VARCHAR},
            </if>
            <if test="price2 != null">
                price2 = #{price2,jdbcType=VARCHAR},
            </if>
            <if test="groupQrCodeInfo != null">
                group_qr_code_info = #{groupQrCodeInfo,jdbcType=VARCHAR},
            </if>
            <if test="orderNumber != null">
                order_number = #{orderNumber,jdbcType=INTEGER},
            </if>
            <if test="brandId != null">
                brand_id = #{brandId,jdbcType=INTEGER},
            </if>
            <if test="language != null">
                language = #{language,jdbcType=VARCHAR},
            </if>
            <if test="region != null">
                region = #{region,jdbcType=VARCHAR},
            </if>
            <if test="pointReadeemType != null">
                point_readeem_type = #{pointReadeemType,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="jp.co.nttdata.sz.web.entity.ProductInfo">
        update mst_products
        set item_id              = #{itemId,jdbcType=VARCHAR},
            quantity             = #{quantity,jdbcType=INTEGER},
            title                = #{title,jdbcType=VARCHAR},
            organization_unit_id = #{organizationUnitId,jdbcType=INTEGER},
            sub_title            = #{subTitle,jdbcType=VARCHAR},
            price                = #{price,jdbcType=DECIMAL},
            prom_price           = #{promPrice,jdbcType=DECIMAL},
            barcode              = #{barcode,jdbcType=VARCHAR},
            sales_volume         = #{salesVolume,jdbcType=INTEGER},
            keywords             = #{keywords,jdbcType=VARCHAR},
            pic_url              = #{picUrl,jdbcType=VARCHAR},
            like_count           = #{likeCount,jdbcType=INTEGER},
            description          = #{description,jdbcType=VARCHAR},
            is_from_brand        = #{isFromBrand,jdbcType=BIT},
            seller_id            = #{sellerId,jdbcType=VARCHAR},
            outer_id             = #{outerId,jdbcType=VARCHAR},
            from_type            = #{fromType,jdbcType=VARCHAR},
            has_real_skus        = #{hasRealSkus,jdbcType=BIT},
            age_scope            = #{ageScope,jdbcType=VARCHAR},
            gender               = #{gender,jdbcType=VARCHAR},
            rfid_code            = #{rfidCode,jdbcType=VARCHAR},
            price2               = #{price2,jdbcType=VARCHAR},
            group_qr_code_info   = #{groupQrCodeInfo,jdbcType=VARCHAR},
            order_number         = #{orderNumber,jdbcType=INTEGER},
            brand_id             = #{brandId,jdbcType=INTEGER},
            language             = #{language,jdbcType=VARCHAR},
            region               = #{region,jdbcType=VARCHAR},
            point_readeem_type   = #{pointReadeemType,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="searchProducts"
            resultType="jp.co.nttdata.sz.web.service.product.dto.ProductSearchOutputDto">
        SELECT
        f.id AS productId,
        f.title AS productTitle,
        f.price AS price,
        e.id AS thingId,
        g.category AS category,
        f.pic_url AS picUrl,
        t4.cost AS cost
        FROM
        mst_shelf a
        LEFT JOIN mst_shelf_layer b ON b.shelf_id = a.id
        LEFT JOIN shelf_cargo_roads c ON c.layer_id = b.layer_id
        LEFT JOIN shelf_cargo_things d ON d.cargo_road_id = c.id
        LEFT JOIN product_skus e ON e.id = d.thing_id
        LEFT JOIN mst_products f ON e.product_id = f.id
        LEFT JOIN multiple_mst_products_category g ON f.id = g.id AND f.belong_store = g.store_id
        LEFT JOIN mst_products_cost t4 on e.product_id = t4.id AND e.belong_store = t4.store_id
        WHERE (f.keywords like concat('%',concat(#{keywords},'%'))
        OR f.title like concat('%',concat(#{keywords},'%'))
        OR f.sub_title like concat('%',concat(#{keywords},'%')))
        <if test="storeId !=null and storeId != ''">
            AND a.belong_store = #{storeId}
        </if>
    </select>


    <resultMap type="jp.co.nttdata.sz.web.service.product.dto.MstProductInfo" id="produceInfo">
        <result column="id" property="id"/>
        <result column="item_id" property="itemId"/>
        <result column="quantity" property="quantity"/>
        <result column="title" property="title"/>
        <result column="organization_unit_id" property="organizationUnitId"/>
        <result column="sub_title" property="subTitle"/>
        <result column="price" property="price"/>
        <result column="prom_price" property="promPrice"/>
        <result column="barcode" property="barcode"/>
        <result column="sales_volume" property="salesVolume"/>
        <result column="keywords" property="keywords"/>
        <result column="pic_url" property="picUrl"/>
        <result column="like_count" property="likeCount"/>
        <result column="description" property="description"/>
        <result column="is_from_brand" property="fromBrand"/>
        <result column="seller_id" property="sellerId"/>
        <result column="outer_id" property="outerId"/>
        <result column="from_type" property="fromType"/>
        <result column="has_real_skus" property="hasRealSkus"/>
        <result column="age_scope" property="ageScope"/>
        <result column="gender" property="gender"/>
        <result column="rfid_code" property="rfidCode"/>
        <result column="price2" property="price2"/>
        <result column="group_qr_code_info" property="groupQrCodeInfo"/>
        <result column="order_number" property="orderNumbe"/>
        <result column="brand_id" property="brandId"/>
        <result column="language" property="language"/>
        <result column="region" property="region"/>
        <result column="point_readeem_type" property="pointRedeemType"/>
        <result column="is_updated" property="updated"/>
        <result column="belong_store" property="belongStore"/>
    </resultMap>

    <resultMap type="jp.co.nttdata.sz.web.service.product.dto.MstProductInfo" id="produceInfoDetail">
        <result column="id" property="id"/>
        <result column="item_id" property="itemId"/>
        <result column="quantity" property="quantity"/>
        <result column="title" property="title"/>
        <result column="organization_unit_id" property="organizationUnitId"/>
        <result column="sub_title" property="subTitle"/>
        <result column="price" property="price"/>
        <result column="prom_price" property="promPrice"/>
        <result column="barcode" property="barcode"/>
        <result column="sales_volume" property="salesVolume"/>
        <result column="keywords" property="keywords"/>
        <result column="pic_url" property="picUrl"/>
        <result column="like_count" property="likeCount"/>
        <result column="description" property="description"/>
        <result column="is_from_brand" property="fromBrand"/>
        <result column="seller_id" property="sellerId"/>
        <result column="outer_id" property="outerId"/>
        <result column="from_type" property="fromType"/>
        <result column="has_real_skus" property="hasRealSkus"/>
        <result column="age_scope" property="ageScope"/>
        <result column="gender" property="gender"/>
        <result column="rfid_code" property="rfidCode"/>
        <result column="price2" property="price2"/>
        <result column="group_qr_code_info" property="groupQrCodeInfo"/>
        <result column="order_number" property="orderNumbe"/>
        <result column="brand_id" property="brandId"/>
        <result column="language" property="language"/>
        <result column="region" property="region"/>
        <result column="point_readeem_type" property="pointRedeemType"/>
        <result column="is_updated" property="updated"/>
        <result column="belong_store" property="belongStore"/>
        <collection property="skus" ofType="jp.co.nttdata.sz.web.entity.ProductSkus">
            <result column="id_2" property="id"/>
            <result column="sku_id_2" property="skuId"/>
            <result column="quantity_2" property="quantity"/>
            <result column="barcode_2" property="barcode"/>
            <result column="props_name_2" property="propsName"/>
            <result column="title_2" property="title"/>
            <result column="price_2" property="price"/>
            <result column="prom_pice_2" property="promPrice"/>
            <result column="sales_volum_2" property="salesVolume"/>
            <result column="like_count_2" property="likeCount"/>
            <result column="key_words_2" property="keyWords"/>
            <result column="pic_url_2" property="picUrl"/>
            <result column="description_2" property="description"/>
            <result column="outer_id_2" property="outerId"/>
            <result column="order_number_2" property="orderNumber"/>
            <result column="from_type_2" property="fromType"/>
            <result column="color_name_2" property="colorName"/>
            <result column="age_scope_2" property="ageScope"/>
            <result column="gender_2" property="gender"/>
            <result column="rfid_code_2" property="rfidCode"/>
            <result column="price2_2" property="price2"/>
            <result column="weight_2" property="weight"/>
            <result column="point_redeem_type_2" property="pointRedeemType"/>
            <result column="product_id_2" property="productId"/>
            <result column="is_updated_2" property="isUpdated"/>
            <result column="belong_store_2" property="belongStore"/>
            <!--			<collection property="picUrl" ofType="jp.co.nttdata.sz.web.entity.ProductImage">-->
            <!--				<result property="propertyName"    column="property_name_3"    />-->
            <!--				<result property="propImgId"    column="prop_img_id_3"    />-->
            <!--				<result property="imageUrl"    column="image_url_3"    />-->
            <!--				<result property="productId"    column="product_id_3"    />-->
            <!--				<result property="belongStore"    column="belong_store_3"    />-->
            <!--			</collection>-->
        </collection>
    </resultMap>
    <select id="searchBasicProducts" resultMap="produceInfo">
        SELECT
        t1.id,
        t1.item_id,
        t1.quantity,
        t1.title,
        t1.organization_unit_id,
        t1.sub_title,
        t1.price,
        t1.prom_price,
        t1.barcode,
        t1.sales_volume,
        t1.keywords,
        t1.pic_url,
        t1.like_count,
        t1.description,
        t1.is_from_brand,
        t1.seller_id,
        t1.outer_id,
        t1.from_type,
        t1.has_real_skus,
        t1.age_scope,
        t1.gender,
        t1.rfid_code,
        t1.price2,
        t1.group_qr_code_info,
        t1.order_number,
        t1.brand_id,
        t1.language,
        t1.region,
        t1.point_readeem_type,
        t1.is_updated,
        t1.belong_store
        FROM
        mst_products AS t1
        WHERE
        (t1.keywords like concat('%',concat(#{keywords},'%'))
        OR t1.title like concat('%',concat(#{keywords},'%'))
        OR t1.sub_title like concat('%',concat(#{keywords},'%')))
        <if test="id !=null and id != ''">
            AND t1.id = #{id}
        </if>
        <if test="storeId !=null and storeId != ''">
            AND t1.belong_store = #{storeId}
        </if>
    </select>

    <select id="searchBasicProductsDetail" resultMap="produceInfoDetail">
        SELECT
        t1.id,
        t1.item_id,
        t1.quantity,
        t1.title,
        t1.organization_unit_id,
        t1.sub_title,
        t1.price,
        t1.prom_price,
        t1.barcode,
        t1.sales_volume,
        t1.keywords,
        t1.pic_url,
        t1.like_count,
        t1.description,
        t1.is_from_brand,
        t1.seller_id,
        t1.outer_id,
        t1.from_type,
        t1.has_real_skus,
        t1.age_scope,
        t1.gender,
        t1.rfid_code,
        t1.price2,
        t1.group_qr_code_info,
        t1.order_number,
        t1.brand_id,
        t1.language,
        t1.region,
        t1.point_readeem_type,
        t1.is_updated,
        t1.belong_store,
        t2.id id_2,
        t2.sku_id sku_id_2,
        t2.quantity quantity_2,
        t2.barcode barcode_2,
        t2.props_name props_name_2,
        t2.title title_2,
        t2.price price_2,
        t2.prom_pice prom_pice_2,
        t2.sales_volum sales_volum_2,
        t2.like_count like_count_2,
        t2.key_words key_words_2,
        t2.pic_url pic_url_2,
        t2.description description_2,
        t2.outer_id outer_id_2,
        t2.order_number order_number_2,
        t2.from_type from_type_2,
        t2.color_name color_name_2,
        t2.age_scope age_scope_2,
        t2.gender gender_2,
        t2.rfid_code rfid_code_2,
        t2.price2 price2_2,
        t2.weight weight_2,
        t2.point_redeem_type point_redeem_type_2,
        t2.product_id product_id_2,
        t2.is_updated is_updated_2,
        t2.belong_store belong_store_2,
        t3.property_name property_name_3,
        t3.prop_img_id prop_img_id_3,
        t3.image_url image_url_3,
        t3.product_id product_id_3,
        t3.is_updated is_updated_3,
        t3.belong_store belong_store_3
        FROM
        mst_products AS t1
        LEFT JOIN product_skus AS t2 ON t2.product_id = t1.id AND t1.belong_store = t2.belong_store
        LEFT JOIN product_prop_imgs AS t3 ON t3.product_id = t2.product_id AND t3.belong_store = t2.belong_store
        WHERE
        1=1
        <if test="id !=null and id != ''">
            AND t1.id = #{id}
        </if>
        <if test="storeId !=null and storeId != ''">
            AND t1.belong_store = #{storeId}
        </if>
    </select>

    <resultMap type="jp.co.nttdata.sz.web.entity.CategoriesThings" id="CategoriesProductSearchResult">
        <result property="category" column="category"/>
        <collection property="productSearchOutputDtoList"
                    ofType="jp.co.nttdata.sz.web.service.product.dto.ProductSearchOutputDto">
            <result property="productId" column="productId"/>
            <result property="productTitle" column="productTitle"/>
            <result property="price" column="price"/>
            <result property="thingId" column="thingId"/>
            <result property="picUrl" column="picUrl"/>
            <result property="hasRealSkus" column="hasRealSkus"/>
            <result property="salesVolume" column="salesVolume"/>
            <result property="barcode" column="barcode"/>
            <result property="itemId" column="itemId"/>
            <result property="cost" column="cost"/>
        </collection>
    </resultMap>

    <select id="searchAllProductsByCategory"
            resultMap="CategoriesProductSearchResult">
        select f.id AS productId,
        f.title AS productTitle,
        f.price AS price,
        e.id AS thingId,
        IFNULL( g.category, '其他' ) as category,
        f.pic_url AS picUrl,
        f.has_real_skus as hasRealSkus,
        f.sales_volume as salesVolume,
        f.barcode as barcode,
        f.item_id as itemId,
        t4.cost AS cost
        from mst_products f left join product_skus e on f.id = e.product_id and f.belong_store = e.belong_store
        left join multiple_mst_products_category g on f.item_id = g.item_id and f.belong_store = g.store_id
        LEFT JOIN mst_products_cost t4 on e.product_id = t4.id AND e.belong_store = t4.store_id

        WHERE
        1=1
        <if test="keywords !=null and keywords != ''">
            AND (f.keywords like concat('%',concat(#{keywords},'%'))
            OR f.title like concat('%',concat(#{keywords},'%'))
            OR f.sub_title like concat('%',concat(#{keywords},'%'))
            OR f.item_id like concat('%',concat(#{keywords},'%')))
        </if>
        <if test="storeId !=null and storeId != ''">
            AND f.belong_store = #{storeId}
        </if>
        <if test="barcode !=null and barcode != ''">
            AND f.barcode = #{barcode}
        </if>
    </select>

    <select id="searchAllProducts"
            resultType="jp.co.nttdata.sz.web.service.product.dto.ProductSearchOutputDto">
        select f.id AS productId,
        f.title AS productTitle,
        f.price AS price,
        e.id AS thingId,
        g.category AS category,
        f.pic_url AS picUrl,
        t4.cost AS cost
        from mst_products f left join product_skus e on f.id = e.product_id and f.belong_store = e.belong_store
        left join multiple_mst_products_category g on f.item_id = g.item_id and f.belong_store = g.store_id
        LEFT JOIN mst_products_cost t4 on e.product_id = t4.id AND e.belong_store = t4.store_id

        WHERE (f.keywords like concat('%',concat(#{keywords},'%'))
        OR f.title like concat('%',concat(#{keywords},'%'))
        OR f.sub_title like concat('%',concat(#{keywords},'%')))
        <if test="storeId !=null and storeId != ''">
            AND f.belong_store = #{storeId}
        </if>
    </select>

    <resultMap type="jp.co.nttdata.sz.web.entity.BarCodeThings" id="ProductSearchResultWithBarCode">
        <result property="barcode" column="barcode"/>
        <collection property="productSearchOutputDtoList"
                    ofType="jp.co.nttdata.sz.web.service.product.dto.ProductSearchOutputDto">
            <result property="productId" column="productId"/>
            <result property="productTitle" column="productTitle"/>
            <result property="price" column="price"/>
            <result property="thingId" column="thingId"/>
            <result property="picUrl" column="picUrl"/>
            <result property="hasRealSkus" column="hasRealSkus"/>
            <result property="salesVolume" column="salesVolume"/>
            <result property="barcode" column="barcode"/>
            <result property="category" column="category"/>
            <result property="itemId" column="itemId"/>
            <result property="cost" column="cost"/>
        </collection>
    </resultMap>

    <select id="searchAllByBarCode"
            resultMap="ProductSearchResultWithBarCode">
        select f.id       AS productId,
               f.title    AS productTitle,
               f.price    AS price,
               f.barcode  AS barcode,
               e.id       AS thingId,
               IFNULL( g.category, '其他' ) as category,
               f.pic_url  AS picUrl,
               t4.cost    AS cost
        from mst_products f
                 left join product_skus e on f.id = e.product_id and f.belong_store = e.belong_store
                 left join multiple_mst_products_category g on f.item_id = g.item_id and f.belong_store = g.store_id
                 LEFT JOIN mst_products_cost t4 on e.product_id = t4.id AND e.belong_store = t4.store_id
        WHERE f.barcode = #{barcode}
          AND f.belong_store = #{storeId}
    </select>
</mapper>
