<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.WxBindingUserDao">

	<select id="hasRegister" parameterType="java.lang.String"
		resultType="java.lang.Boolean">
		SELECT COUNT(*)>0 FROM wx_binding_user WHERE open_id =#{openId}
	</select>
	
	<insert id="inserWxBindingUser" parameterType="jp.co.nttdata.sz.web.entity.WxBindingUserEntity">
	INSERT INTO wx_binding_user 
	(open_id,user_id,wx_group)
	VALUES(#{openId},#{userId},#{group})
	</insert>
</mapper>