<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.dao.StoreUserDao">
  <resultMap id="BaseResultMap" type="jp.co.nttdata.sz.web.entity.StoreUser">
    <id column="user_store_id" jdbcType="INTEGER" property="userStoreId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="flag" jdbcType="BIT" property="flag" />
  </resultMap>

  <resultMap id="BaseStoreResultMap" type="jp.co.nttdata.sz.web.controller.iosuser.dto.StoreDto">
    <id column="store_adress" jdbcType="VARCHAR" property="storeAdress" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
  </resultMap>

  <sql id="Base_Column_List">
    user_store_id, user_id, store_id, flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from store_user
    where user_store_id = #{userStoreId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from store_user
    where user_store_id = #{userStoreId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="jp.co.nttdata.sz.web.entity.StoreUser">
    insert into store_user (user_store_id, user_id, store_id,
      flag)
    values (#{userStoreId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{storeId,jdbcType=VARCHAR},
      #{flag,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="jp.co.nttdata.sz.web.entity.StoreUser">
    insert into store_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userStoreId != null">
        user_store_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="flag != null">
        flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userStoreId != null">
        #{userStoreId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
        #{flag,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="jp.co.nttdata.sz.web.entity.StoreUser">
    update store_user
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
        flag = #{flag,jdbcType=BIT},
      </if>
    </set>
    where user_store_id = #{userStoreId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="jp.co.nttdata.sz.web.entity.StoreUser">
    update store_user
    set user_id = #{userId,jdbcType=INTEGER},
      store_id = #{storeId,jdbcType=VARCHAR},
      flag = #{flag,jdbcType=BIT}
    where user_store_id = #{userStoreId,jdbcType=INTEGER}
  </update>
  <select id="selectStoresByUserId"  resultMap="BaseStoreResultMap">
    SELECT
      t3.*
    FROM
      sys_user t1
        INNER JOIN store_user t2 ON t1.user_id = t2.user_id
        INNER JOIN mst_store t3 ON t2.store_id = t3.store_id
    WHERE
      t1.user_id = #{userId}
  </select>

  <select id="selectOperationStoresByUserId"  resultType="java.lang.String">
    select t1.store_id from store_operation_user t1 where t1.user_id = #{userId}
  </select>

    <select id="selectStoreUser"  parameterType="jp.co.nttdata.sz.web.entity.StoreUser" resultType="java.lang.Integer">
      select count(*) from store_user where store_id = #{storeId} and user_id = #{userId}
    </select>
</mapper>
