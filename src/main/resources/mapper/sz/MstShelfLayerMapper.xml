<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.mapper.MstShelfLayerMapper">
        <resultMap type="jp.co.nttdata.sz.web.entity.MstShelfLayer"
                id="MstShelfLayerResult">
                <result property="layerId"
                        column="layer_id" />
                <result property="shelfId"
                        column="shelf_id" />
                <result property="layerHeight"
                        column="layer_height" />
                <result property="startPointZ"
                        column="start_point_z" />
                <result property="index"
                        column="index" />
                <result property="cargoRoadCount"
                        column="cargo_road_count" />
                <result property="isUpdated"
                        column="is_updated" />
                <result property="belongStore"
                        column="belong_store" />
        </resultMap>

        <sql id="selectMstShelfLayerVo"> select layer_id, shelf_id, layer_height, start_point_z,
                `index`, cargo_road_count, is_updated, belong_store from mst_shelf_layer </sql>

        <select id="selectMstShelfLayerList"
                parameterType="jp.co.nttdata.sz.web.entity.MstShelfLayer"
                resultMap="MstShelfLayerResult">
                <include refid="selectMstShelfLayerVo" />
        <where>
                        <if test="shelfId != null ">and shelf_id = #{shelfId}</if>
            <if
                                test="layerHeight != null ">and layer_height = #{layerHeight} </if>
            <if
                                test="startPointZ != null ">and start_point_z = #{startPointZ} </if>
            <if
                                test="index != null ">and `index` = #{index} </if>
            <if
                                test="cargoRoadCount != null "> and cargo_road_count =
                #{cargoRoadCount} </if>
            <if test="isUpdated != null ">and is_updated = #{isUpdated} </if>
            <if
                                test="belongStore != null  and belongStore != ''">and belong_store =
                #{belongStore} </if>
                </where>
        </select>

        <resultMap type="jp.co.nttdata.sz.web.entity.MstShelfLayer"
                id="MstShelfLayerResultWithRoad">
                <result property="layerId"
                        column="layer_id" />
                <result property="shelfId"
                        column="shelf_id" />
                <result property="layerHeight"
                        column="layer_height" />
                <result property="startPointZ"
                        column="start_point_z" />
                <result property="index"
                        column="index" />
                <result property="cargoRoadCount"
                        column="cargo_road_count" />
                <result property="isUpdated"
                        column="is_updated" />
                <result property="belongStore"
                        column="belong_store" />
                <collection property="shelfCargoRoadList"
                        ofType="jp.co.nttdata.sz.web.entity.ShelfCargoRoad">
                        <result property="deviceId"
                                column="device_id" />
                        <result property="cargoType"
                                column="cargo_type" />
                        <result property="cargoRoadId"
                                column="cargo_road_id" />
                        <result property="startLength"
                                column="start_length" />
                        <result property="sensors"
                                column="sensors" />
                        <result property="grossWeight"
                                column="gross_weight" />
                        <result property="weight"
                                column="weight" />
                        <result property="warningWeight"
                                column="warning_weight" />
                        <result property="selfWeight"
                                column="self_weight" />
                        <result property="name"
                                column="name" />
                        <result property="outerId"
                                column="outer_id" />
                        <result property="isEnabled"
                                column="is_enabled" />
                        <result property="stockCapacity"
                                column="stock_capacity" />
                        <result property="description"
                                column="description" />
                        <result property="extensionData"
                                column="extension_data" />
                        <result property="resolutionLength"
                                column="resolution_length" />
                        <result property="resolutionWidth"
                                column="resolution_width" />
                        <result property="startPointX"
                                column="start_point_x" />
                        <result property="layerId"
                                column="t2_layer_id" />
                        <result property="resolutionHeight"
                                column="resolution_height" />
                        <result property="startPointZ"
                                column="t2_start_point_z" />
                        <result property="spanRows"
                                column="span_rows" />
                        <result property="spanColums"
                                column="span_colums" />
                        <result property="isUpdated"
                                column="t2_is_updated" />
                        <result property="belongStore"
                                column="t2_belong_store" />
                        <result property="mergeStatus"
                                column="merge_status" />
                        <result property="mergeGroupId"
                                column="merge_group_id" />
                        <result property="mergedWidth"
                                column="merge_width" />
                </collection>
        </resultMap>
        <!-- 更新后的货道查询，过滤被合并的货道 -->
        <select id="selectMstShelfLayerListWithRoad"
                parameterType="jp.co.nttdata.sz.web.entity.MstShelfLayer"
                resultMap="MstShelfLayerResultWithRoad"> SELECT t1.layer_id, t1.shelf_id,
                t1.layer_height, t1.start_point_z, t1.`index`, t1.cargo_road_count, t1.is_updated,
                t1.belong_store, t2.device_id, t2.cargo_type, t2.cargo_road_id, t2.start_length,
                t2.sensors, t2.gross_weight, t2.weight, t2.warning_weight, t2.self_weight, t2.name,
                t2.outer_id, t2.is_enabled, t2.stock_capacity, t2.description, t2.extension_data,
                t2.resolution_length, t2.layer_id as t2_layer_id, t2.resolution_width,
                t2.start_point_x, t2.resolution_height, t2.start_point_z as t2_start_point_z,
                t2.span_rows, t2.span_colums, t2.is_updated as t2_is_updated, t2.belong_store as
                t2_belong_store, t2.merge_status, t2.merge_group_id, t2.merge_width FROM
                mst_shelf_layer t1 LEFT JOIN shelf_cargo_roads t2 ON t1.layer_id = t2.layer_id AND
                t1.shelf_id = t2.device_id AND t1.belong_store = t2.belong_store <where>
                        <if
                                test="shelfId != null">and t1.shelf_id = #{shelfId}</if>
            <if
                                test="layerHeight != null">and t1.layer_height = #{layerHeight}</if>
            <if
                                test="startPointZ != null">and t1.start_point_z = #{startPointZ}</if>
            <if
                                test="index != null">and t1.`index` = #{index}</if>
            <if
                                test="cargoRoadCount != null">and t1.cargo_road_count =
                #{cargoRoadCount}</if>
            <if
                                test="isUpdated != null">and t1.is_updated = #{isUpdated}</if>
            <if
                                test="belongStore != null and belongStore != ''">and t1.belong_store
                = #{belongStore}</if>
                </where>
        </select>
        <!-- 查询可合并的货道（管理功能用）todo 2025-06-18 -->
        <select id="selectMergeableCargoRoads"
                parameterType="map"
                resultType="jp.co.nttdata.sz.web.entity.ShelfCargoRoad"> SELECT * FROM
                shelf_cargo_roads WHERE layer_id = #{layerId} AND merge_status = 0 -- 只显示未参与合并的货道
                ORDER BY start_point_x </select>

        <!-- 根据合并组ID查询货道 todo 2025-06-18 -->
        <select id="selectByMergeGroupId"
                parameterType="string"
                resultType="jp.co.nttdata.sz.web.entity.ShelfCargoRoad"> SELECT * FROM
                shelf_cargo_roads WHERE merge_group_id = #{mergeGroupId} </select>

        <select id="selectMstShelfLayer"
                parameterType="jp.co.nttdata.sz.web.entity.MstShelfLayer"
                resultMap="MstShelfLayerResult">
                <include refid="selectMstShelfLayerVo" /> where layer_id = #{layerId} </select>

        <insert id="insertMstShelfLayer"
                parameterType="jp.co.nttdata.sz.web.entity.MstShelfLayer"
                useGeneratedKeys="true"
                keyProperty="layerId">insert into mst_shelf_layer <trim prefix="("
                        suffix=")"
                        suffixOverrides=",">
                        <if test="shelfId != null">shelf_id,</if>
            <if test="layerHeight != null">
                layer_height,</if>
            <if
                                test="startPointZ != null">start_point_z, </if>
            <if
                                test="index != null">`index`,</if>
            <if
                                test="cargoRoadCount != null">cargo_road_count, </if>
            <if
                                test="isUpdated != null"> is_updated, </if>
            <if
                                test="belongStore != null">belong_store,</if>
                </trim>
        <trim
                        prefix="values ("
                        suffix=")"
                        suffixOverrides=",">
                        <if test="shelfId != null">#{shelfId},</if>
            <if test="layerHeight != null">
                #{layerHeight}, </if>
            <if test="startPointZ != null">#{startPointZ},</if>
            <if
                                test="index != null">#{index}, </if>
            <if
                                test="cargoRoadCount != null"> #{cargoRoadCount}, </if>
            <if
                                test="isUpdated != null">#{isUpdated},</if>
            <if
                                test="belongStore != null">#{belongStore}, </if>
                </trim>
        </insert>


        <update id="updateMstShelfLayer"
                parameterType="jp.co.nttdata.sz.web.entity.MstShelfLayer"> update mst_shelf_layer <trim
                        prefix="SET"
                        suffixOverrides=",">
                        <if test="shelfId != null">shelf_id = #{shelfId},</if>
            <if
                                test="layerHeight != null">layer_height = #{layerHeight}, </if>
            <if
                                test="startPointZ != null">start_point_z = #{startPointZ},</if>
            <if
                                test="index != null">`index` = #{index}, </if>
            <if
                                test="cargoRoadCount != null">cargo_road_count = #{cargoRoadCount}, </if>
            <if
                                test="isUpdated != null">is_updated = #{isUpdated},</if>
            <if
                                test="belongStore != null">belong_store = #{belongStore}, </if>
                </trim>
                where layer_id = #{layerId} </update>


        <delete id="deleteMstShelfLayer"
                parameterType="jp.co.nttdata.sz.web.entity.MstShelfLayer"> delete from
                mst_shelf_layer where layer_id = #{layerId} </delete>

        <delete id="batchDeleteMstShelfLayer"
                parameterType="jp.co.nttdata.sz.web.entity.MstShelfLayer"> delete from
                mst_shelf_layer where layer_id in <foreach item="item"
                        collection="mstShelfLayers"
                        open="("
                        separator=","
                        close=")">#{item.layerId} </foreach>
        </delete>
</mapper>