<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.api.dao.SalesStatisticDao">
	<resultMap id="salesData" type="jp.co.nttdata.sz.api.entity.SalesDataEntity">
		<result property="totalAmount" column="total_amount" jdbcType="DECIMAL" />
		<result property="successCount" column="success_count" jdbcType="INTEGER" />
		<result property="totalOrder" column="total_order" jdbcType="INTEGER"/>
		<result property="lastDate" column="lastDate" jdbcType="VARCHAR" />
		<result property="startDate" column="startDate" jdbcType="VARCHAR"/>
		<result property="endDate" column="endDate" jdbcType="VARCHAR" />
		<result property="lastMonth" column="lastMonth" jdbcType="VARCHAR"/>
	</resultMap>
	<resultMap id="orderInfoData" type="jp.co.nttdata.sz.api.entity.OrderDataEntity">
		<result property="enterCount" column="enter_count" jdbcType="INTEGER" />
		<result property="orderCount" column="order_count" jdbcType="INTEGER" />
		<result property="successCount" column="success_count" jdbcType="INTEGER" />
		<result property="noorderCount" column="noorder_count" jdbcType="INTEGER" />
		<result property="failMoreCount" column="fail_more_count" jdbcType="INTEGER" />
		<result property="failLessCount" column="fail_less_count" jdbcType="INTEGER" />
		<result property="lastDate" column="lastDate" jdbcType="VARCHAR" />
	</resultMap>

	<resultMap id="productInfo" type="jp.co.nttdata.sz.api.entity.ProductRankEntity">
		<result property="skuId" column="sku_id" jdbcType="INTEGER" />
		<result property="title" column="title" jdbcType="VARCHAR" />
		<result property="sellCount" column="sell_count" jdbcType="INTEGER" />
		<result property="sellAmount" column="sell_amount" jdbcType="DECIMAL" />
	</resultMap>

	<resultMap type="jp.co.nttdata.sz.web.service.shelf.Dto.LackShelfCargoRoadDto" id="LackShelfCargoRoadResult">
		<result property="roadName"  column="road_name"    />
		<result property="deviceName"  column="device_name"    />
		<result property="productName"  column="product_name"    />
		<result property="weight"    column="weight"    />
	</resultMap>

	<resultMap type="jp.co.nttdata.sz.api.entity.DailyDauEntity" id="dauResult">
		<result property="dauCount"  column="dau_count"    />
		<result property="dayTime"  column="day_time"    />
	</resultMap>

	<resultMap type="jp.co.nttdata.sz.api.entity.UserInfoEntity" id="userInfo">
		<result property="userStaffNumber"  column="user_staff_number"    />
		<result property="userName"  column="user_name"    />
	</resultMap>

	<select id="selectSalesStatics" parameterType="jp.co.nttdata.sz.api.controller.manager.dto.SalesStatisticsInputDto" resultMap="salesData">
		SELECT
		TRUNCATE(ifnull(SUM(prom_amount),0),2) as total_amount,
		Count(*) as total_order,
		IFNULL(sum(if(((t1.order_status in ('4', '5', '6', '8')) and (IFNULL(evaluation_tags,"完全正确")= "完全正确")),1,0)),0) as success_count,
		DATE_FORMAT( date_add( now( ), INTERVAL - 1 DAY ), '%Y.%m.%d' ) AS lastDate,
		DATE_FORMAT( DATE_SUB( curdate( ), INTERVAL date_format( curdate( ), '%w' ) + 6 DAY ), '%Y.%m.%d' ) AS startDate,
		DATE_FORMAT( DATE_SUB( curdate( ), INTERVAL date_format( curdate( ), '%w' ) DAY ), '%Y.%m.%d' ) AS endDate,
		DATE_FORMAT( DATE_SUB( curdate( ), INTERVAL 1 MONTH ), '%m' ) AS lastMonth
		FROM
		mst_order t1
		WHERE
		t1.order_status IN ( '4', '5', '6','7', '8','9' ) and
		<if test="type == 0">
			t1.create_time LIKE CONCAT(DATE_FORMAT(date_add(now(), interval -1 day),'%Y-%m-%d'),"%")
		</if>
		<if test="type == 1">
			YEARWEEK(t1.create_time,1) = YEARWEEK(date_add(now(), interval -1 day),1)
		</if>
		<if test="type == 2">
			t1.create_time LIKE CONCAT(DATE_FORMAT(date_add(now(), interval -1 day),'%Y-%m'),"%")
		</if>
	</select>
	<select id="selectOrderStatics" resultMap="orderInfoData">
		SELECT
			count(*) as enter_count,
			IFNULL(sum(if(t2.shopcar_id is NULL,0,1)),0) as order_count,
			IFNULL(sum(if(t2.shopcar_id is NULL,1,0)),0) as noorder_count,
			IFNULL(sum(if(((t2.order_status in ('4', '5', '6', '8')) and (IFNULL(evaluation_tags,"完全正确")= "完全正确")),1,0)),0) as success_count,
			IFNULL(sum(if(((t2.order_status in ('4', '5', '6', '8')) and (t2.evaluation_tags="订单多算")),1,0)),0) as fail_more_count,
			IFNULL(sum(if(((t2.order_status in ('4', '5', '6', '8')) and (t2.evaluation_tags="订单少算")),1,0)),0) as fail_less_count,
			DATE_FORMAT( date_add( now( ), INTERVAL - 1 DAY ), '%Y.%m.%d' ) AS lastDate
		FROM
			mst_shopcar t1
				LEFT JOIN mst_order t2 on t2.shopcar_id = t1.shopcar_id and   DATE_FORMAT( date_add( t2.create_time, INTERVAL - 1 DAY ), '%Y-%m-%d' ) =  DATE_FORMAT( date_add( now( ), INTERVAL - 1 DAY ), '%Y-%m-%d' )
		WHERE
				DATE_FORMAT( date_add(t1.end_time, INTERVAL - 1 DAY ), '%Y-%m-%d' ) =  DATE_FORMAT( date_add( now( ), INTERVAL - 1 DAY ), '%Y-%m-%d' )
	</select>

	<select id="selectProductRankList" parameterType="jp.co.nttdata.sz.api.controller.manager.dto.ProductRankInputDto" resultMap="productInfo">
		SELECT
		t4.id as sku_id,
		t4.title,
		IFNULL(t3.sell_count,0) as sell_count,
		TRUNCATE(IFNULL(t3.sell_amount,0),2) as sell_amount
		from (
		SELECT
		t1.sku_id,
		count( t1.quantity ) AS sell_count,
		sum(t1.quantity * if(t1.prom_price=0,t1.price,t1.prom_price)) as sell_amount
		FROM
		order_items t1
		LEFT JOIN mst_order t2 ON t1.order_id = t2.order_id
		WHERE
		t2.order_status in ( '4', '5', '6','7','8','9') and t2.create_time > DATE_FORMAT(date_add( now( ), INTERVAL - #{day} DAY ),'%Y-%m-%d')
		GROUP BY
		t1.sku_id
		) as t3
		right JOIN product_skus t4 on t3.sku_id = t4.id
		order by sell_count
		<if test="type == 0">
			asc
		</if>
		<if test="type == 1">
			desc
		</if>
		limit #{limit}
	</select>

	<select id="selectLackShelfCargoRoad" resultMap="LackShelfCargoRoadResult">
		SELECT
		t1.NAME AS road_name,
		t2.NAME AS device_name,
		t3.thing_name AS product_name,
		IFNULL( gross_weight, 0 ) AS weight
		FROM
		shelf_cargo_roads t1
		LEFT JOIN mst_shelf t2 ON t1.device_id = t2.id
		LEFT JOIN shelf_cargo_things t3 ON t3.cargo_road_id = t1.id
		LEFT JOIN shelf_cargo_roads_ext t4 ON t4.cargo_road_id = t1.id
		WHERE
		t4.warning_weight IS NOT NULL
		AND IFNULL( gross_weight, 0 ) &lt;= IFNULL( t4.warning_weight, 0 )
	</select>
	<select id="selectShopDau"  resultMap="dauResult">
		SELECT
		count(DISTINCT(t1.member_id)) as dau_count,
		DATE_FORMAT(t1.start_time,"%Y-%m-%d") as day_time
		FROM
		mst_shopcar t1
		WHERE DATEDIFF(now( ), t1.start_time ) &lt;= 365
		GROUP BY DATE_FORMAT(t1.start_time,"%Y-%m-%d")
		ORDER BY t1.start_time desc
		<if test="day != null">
			limit #{day}
		</if>
<!--		SELECT-->
<!--		count( DISTINCT ( t2.member_id )) as visit_count,-->
<!--		t2.diff_day-->
<!--		FROM-->
<!--		(-->
<!--		SELECT-->
<!--		member_id,-->
<!--		start_time,-->
<!--		CASE-->

<!--		WHEN DATEDIFF( now(), t1.start_time ) &lt;= 30 AND DATEDIFF( now(), t1.start_time ) &gt; 7 THEN-->
<!--		1-->
<!--		WHEN DATEDIFF( now(), t1.start_time ) &lt;= 7 AND DATEDIFF( now(), t1.start_time ) &gt; 3 THEN-->
<!--		2-->
<!--		WHEN DATEDIFF( now(), t1.start_time ) &lt;= 3 THEN-->
<!--		3 ELSE 4-->
<!--		END AS diff_day-->
<!--		FROM-->
<!--		mst_shopcar t1-->
<!--		WHERE-->
<!--		DATEDIFF( now(), t1.start_time ) &lt;= 30-->
<!--		) t2-->
<!--		GROUP BY-->
<!--		t2.diff_day-->
	</select>


	<select id="getUserInfo" resultMap="userInfo">
		SELECT
		staff_number as user_staff_number,
		user_name
		FROM
		sys_user
		WHERE
		user_id = #{userId}
	</select>

</mapper>
