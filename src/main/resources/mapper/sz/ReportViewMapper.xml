<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.ReportViewDao">
	<select id="searchDailyReprot"
		resultType="jp.co.nttdata.sz.web.entity.DailyReportEntity"
		parameterType="jp.co.nttdata.sz.web.controller.report.dto.DailyReportSearchInputDto">
		SELECT user_name AS userName,
		wx_group AS wxGroup,
		start_time AS startTime,
		end_time AS endTime,
		shopcar_id AS shopcarId,
		order_id AS orderId,
		order_status AS orderStatus,
		settlement_type AS settelementType,
		amount AS amount,
		content AS content,
		order_evaluation AS orderEvaluation,
		evaluation_tags AS evaluationTags,
		question_type AS questionType,
		question_des AS questionDes
		FROM daily_report 
		WHERE 
		1=1
		<if test="startTime!=null and startTime!=''">
		AND start_time >= concat(#{startTime},' 00:00')
		</if>
		<if test="endTime!=null and endTime!=''">
		AND end_time &lt;= concat(#{endTime},' 23:59')
		</if>
		<if test="orderStatus!=null and orderStatus!=''">
		AND order_status =#{orderStatus}
		</if>
		<if test="userName!=null and userName!=''">
		AND user_name like  concat(concat('%',#{userName}),'%')
		</if>
		<if test="storeId!=null and storeId!=''">
			AND consumer_store_id =#{storeId}
		</if>
	</select>
	
	<update id="updateRemarkData" parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportRemarkInputDto">
	   UPDATE test_report SET
	   shopcar_id = #{shopcarId}
	   <if test="questionType!=null and questionType!=''">
	   ,question_type=#{questionType}
	   </if>
	   <if test="questionDes!=null and questionDes!=''">
	   ,question_des=#{questionDes}
	   </if>
	   ,create_time =CURRENT_TIMESTAMP
	   WHERE shopcar_id =#{shopcarId}
	</update>
	
	<insert id="insertRemarkData" parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportRemarkInputDto">
	  INSERT INTO test_report
	  (shopcar_id,question_type,question_des,create_time)
	  VALUES
	  (#{shopcarId},#{questionType},#{questionDes},CURRENT_TIMESTAMP)
	</insert>
	
	<select id="hasRemark" parameterType="java.lang.String" resultType="java.lang.Boolean">
	 SELECT COUNT(*) FROM test_report WHERE shopcar_id =#{shopcarId}
	 </select>
</mapper>
