<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.dao.ProductSkusMapper">

    <resultMap type="jp.co.nttdata.sz.web.entity.ProductSkus" id="ProductSkusResult">
        <result property="id"    column="id"    />
        <result property="skuId"    column="sku_id"    />
        <result property="quantity"    column="quantity"    />
        <result property="barcode"    column="barcode"    />
        <result property="propsName"    column="props_name"    />
        <result property="title"    column="title"    />
        <result property="price"    column="price"    />
        <result property="promPrice"    column="prom_pice"    />
        <result property="salesVolume"    column="sales_volum"    />
        <result property="likeCount"    column="like_count"    />
        <result property="keyWords"    column="key_words"    />
        <result property="picUrl"    column="pic_url"    />
        <result property="description"    column="description"    />
        <result property="outerId"    column="outer_id"    />
        <result property="orderNumber"    column="order_number"    />
        <result property="fromType"    column="from_type"    />
        <result property="colorName"    column="color_name"    />
        <result property="ageScope"    column="age_scope"    />
        <result property="gender"    column="gender"    />
        <result property="rfidCode"    column="rfid_code"    />
        <result property="price2"    column="price2"    />
        <result property="weight"    column="weight"    />
        <result property="stock"    column="stock"    />
        <result property="pointRedeemType"    column="point_redeem_type"    />
        <result property="productId"    column="product_id"    />
        <result property="isUpdated"    column="is_updated"    />
        <result property="belongStore"    column="belong_store"    />
        <result property="itemId"    column="item_id"    />
        <result property="category"    column="category"    />
    </resultMap>

    <sql id="selectProductSkusVo">
        select id, sku_id, quantity, barcode, props_name, title, price, prom_pice, sales_volum, like_count, key_words, pic_url, description, outer_id, order_number, from_type, color_name, age_scope, gender, rfid_code, price2, weight, point_redeem_type, product_id, is_updated, belong_store, stock from product_skus
    </sql>

    <select id="selectProductSkusList" parameterType="jp.co.nttdata.sz.web.entity.ProductSkus" resultMap="ProductSkusResult">
        select
        t1.id,
        t1.sku_id,
        t1.quantity,
        t1.barcode,
        t1.props_name,
        t1.title,
        t1.price,
        t1.prom_pice,
        t1.sales_volum,
        t1.like_count,
        t1.key_words,
        t1.pic_url,
        t1.description,
        t1.outer_id,
        t1.order_number,
        t1.from_type,
        t1.color_name,
        t1.age_scope,
        t1.gender,
        t1.rfid_code,
        t1.price2,
        t1.weight,
        t1.point_redeem_type,
        t1.product_id,
        t1.is_updated,
        t1.belong_store,
        IFNULL(t1.stock, 0 ) as stock ,
        t2.item_id,
        IFNULL( t3.category, '其他' ) as category
        FROM
        product_skus t1
        LEFT JOIN mst_products t2 ON t1.product_id = t2.id and t1.belong_store=t2.belong_store
        left join multiple_mst_products_category t3 on t1.product_id= t3.id and t1.belong_store = t3.store_id
        <where>
            <if test="skuId != null  and skuId != ''"> and t1.sku_id = #{skuId}</if>
            <if test="quantity != null "> and t1.quantity = #{quantity}</if>
            <if test="barcode != null  and barcode != ''"> and t1.barcode = #{barcode}</if>
            <if test="propsName != null  and propsName != ''"> and t1.props_name like concat('%', #{propsName}, '%')</if>
            <if test="title != null  and title != ''"> and t1.title = #{title}</if>
            <if test="price != null "> and t1.price = #{price}</if>
            <if test="promPrice != null "> and t1.prom_pice = #{promPrice}</if>
            <if test="salesVolume != null "> and t1.sales_volume = #{salesVolume}</if>
            <if test="likeCount != null "> and t1.like_count = #{likeCount}</if>
            <if test="keyWords != null  and keyWords != ''"> and t1.key_words = #{keyWords}</if>
            <if test="picUrl != null  and picUrl != ''"> and t1.pic_url = #{picUrl}</if>
            <if test="description != null  and description != ''"> and t1.description = #{description}</if>
            <if test="outerId != null  and outerId != ''"> and t1.outer_id = #{outerId}</if>
            <if test="orderNumber != null "> and t1.order_number = #{orderNumber}</if>
            <if test="fromType != null  and fromType != ''"> and t1.from_type = #{fromType}</if>
            <if test="colorName != null  and colorName != ''"> and t1.color_name like concat('%', #{colorName}, '%')</if>
            <if test="ageScope != null  and ageScope != ''"> and t1.age_scope = #{ageScope}</if>
            <if test="gender != null  and gender != ''"> and t1.gender = #{gender}</if>
            <if test="rfidCode != null  and rfidCode != ''"> and t1.rfid_code = #{rfidCode}</if>
            <if test="price2 != null  and price2 != ''"> and t1.price2 = #{price2}</if>
            <if test="weight != null "> and t1.weight = #{weight}</if>
            <if test="pointRedeemType != null  and pointRedeemType != ''"> and t1.point_redeem_type = #{pointRedeemType}</if>
            <if test="productId != null "> and t1.product_id = #{productId}</if>
            <if test="isUpdated != null "> and t1.is_updated = #{isUpdated}</if>
            <if test="belongStore != null "> and t1.belong_store = #{belongStore}</if>
        </where>
    </select>

    <select id="selectProductSkusById"  resultMap="ProductSkusResult">
        <include refid="selectProductSkusVo"/>
        where id = #{id}
        and belong_store = #{storeId}
    </select>

    <select id="getIdList" resultType="java.lang.Integer">
        SELECT
            id
        FROM
            product_skus
        WHERE
            product_id = #{productId}
    </select>
    <select id="getSkuMaxId" resultType="java.lang.Integer">
        select max(id)+1 from product_skus where belong_store =#{storeId}
    </select>

    <insert id="insertProductSkus" parameterType="jp.co.nttdata.sz.web.entity.ProductSkus">
        insert into product_skus
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="skuId != null">sku_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="barcode != null">barcode,</if>
            <if test="propsName != null">props_name,</if>
            <if test="title != null">title,</if>
            <if test="price != null">price,</if>
            <if test="promPrice != null">prom_pice,</if>
            <if test="salesVolume != null">sales_volum,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="keyWords != null">key_words,</if>
            <if test="picUrl != null">pic_url,</if>
            <if test="description != null">description,</if>
            <if test="outerId != null">outer_id,</if>
            <if test="orderNumber != null">order_number,</if>
            <if test="fromType != null">from_type,</if>
            <if test="colorName != null">color_name,</if>
            <if test="ageScope != null">age_scope,</if>
            <if test="gender != null">gender,</if>
            <if test="rfidCode != null">rfid_code,</if>
            <if test="price2 != null">price2,</if>
            <if test="weight != null">weight,</if>
            <if test="stock != null">stock,</if>
            <if test="pointRedeemType != null">point_redeem_type,</if>
            <if test="productId != null">product_id,</if>
            <if test="isUpdated != null">is_updated,</if>
            <if test="belongStore != null">belong_store,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="skuId != null">#{skuId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="barcode != null">#{barcode},</if>
            <if test="propsName != null">#{propsName},</if>
            <if test="title != null">#{title},</if>
            <if test="price != null">#{price},</if>
            <if test="promPrice != null">#{promPrice},</if>
            <if test="salesVolume != null">#{salesVolume},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="keyWords != null">#{keyWords},</if>
            <if test="picUrl != null">#{picUrl},</if>
            <if test="description != null">#{description},</if>
            <if test="outerId != null">#{outerId},</if>
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="fromType != null">#{fromType},</if>
            <if test="colorName != null">#{colorName},</if>
            <if test="ageScope != null">#{ageScope},</if>
            <if test="gender != null">#{gender},</if>
            <if test="rfidCode != null">#{rfidCode},</if>
            <if test="price2 != null">#{price2},</if>
            <if test="weight != null">#{weight},</if>
            <if test="stock != null">#{stock},</if>
            <if test="pointRedeemType != null">#{pointRedeemType},</if>
            <if test="productId != null">#{productId},</if>
            <if test="isUpdated != null">#{isUpdated},</if>
            <if test="belongStore != null">#{belongStore},</if>
         </trim>
    </insert>

    <update id="updateProductSkus" parameterType="jp.co.nttdata.sz.web.entity.ProductSkus">
        update product_skus
        <trim prefix="SET" suffixOverrides=",">
            <if test="skuId != null">sku_id = #{skuId},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="barcode != null">barcode = #{barcode},</if>
            <if test="propsName != null">props_name = #{propsName},</if>
            <if test="title != null">title = #{title},</if>
            <if test="price != null">price = #{price},</if>
            <if test="promPrice != null">prom_pice = #{promPrice},</if>
            <if test="salesVolume != null">sales_volum = #{salesVolume},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="keyWords != null">key_words = #{keyWords},</if>
            <if test="picUrl != null">pic_url = #{picUrl},</if>
            <if test="description != null">description = #{description},</if>
            <if test="outerId != null">outer_id = #{outerId},</if>
            <if test="orderNumber != null">order_number = #{orderNumber},</if>
            <if test="fromType != null">from_type = #{fromType},</if>
            <if test="colorName != null">color_name = #{colorName},</if>
            <if test="ageScope != null">age_scope = #{ageScope},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="rfidCode != null">rfid_code = #{rfidCode},</if>
            <if test="price2 != null">price2 = #{price2},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="stock != null">stock = #{stock},</if>
            <if test="pointRedeemType != null">point_redeem_type = #{pointRedeemType},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="isUpdated != null">is_updated = #{isUpdated},</if>
        </trim>
        where id = #{id} and belong_store = #{belongStore}
    </update>

    <delete id="deleteProductSkusById" parameterType="Long">
        delete from product_skus where id = #{id}
    </delete>

    <delete id="deleteProductSkusByIds" parameterType="String">
        delete from product_skus where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and belong_store = #{storeId}
    </delete>


    <insert id="importProductSkus">
        insert into product_skus
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="skuId != null">sku_id,</if>
                <if test="quantity != null">quantity,</if>
                <if test="barcode != null">barcode,</if>
                <if test="propsName != null">props_name,</if>
                <if test="title != null">title,</if>
                <if test="price != null">price,</if>
                <if test="promPrice != null">prom_pice,</if>
                <if test="salesVolume != null">sales_volum,</if>
                <if test="likeCount != null">like_count,</if>
                <if test="keyWords != null">key_words,</if>
                <if test="picUrl != null">pic_url,</if>
                <if test="description != null">description,</if>
                <if test="outerId != null">outer_id,</if>
                <if test="orderNumber != null">order_number,</if>
                <if test="fromType != null">from_type,</if>
                <if test="colorName != null">color_name,</if>
                <if test="ageScope != null">age_scope,</if>
                <if test="gender != null">gender,</if>
                <if test="rfidCode != null">rfid_code,</if>
                <if test="price2 != null">price2,</if>
                <if test="weight != null">weight,</if>
                <if test="stock != null">stock,</if>
                <if test="pointRedeemType != null">point_redeem_type,</if>
                <if test="productId != null">product_id,</if>
                <if test="isUpdated != null">is_updated,</if>
                <if test="belongStore != null">belong_store,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id},</if>
                <if test="skuId != null">#{skuId},</if>
                <if test="quantity != null">#{quantity},</if>
                <if test="barcode != null">#{barcode},</if>
                <if test="propsName != null">#{propsName},</if>
                <if test="title != null">#{title},</if>
                <if test="price != null">#{price},</if>
                <if test="promPrice != null">#{promPrice},</if>
                <if test="salesVolume != null">#{salesVolume},</if>
                <if test="likeCount != null">#{likeCount},</if>
                <if test="keyWords != null">#{keyWords},</if>
                <if test="picUrl != null">#{picUrl},</if>
                <if test="description != null">#{description},</if>
                <if test="outerId != null">#{outerId},</if>
                <if test="orderNumber != null">#{orderNumber},</if>
                <if test="fromType != null">#{fromType},</if>
                <if test="colorName != null">#{colorName},</if>
                <if test="ageScope != null">#{ageScope},</if>
                <if test="gender != null">#{gender},</if>
                <if test="rfidCode != null">#{rfidCode},</if>
                <if test="price2 != null">#{price2},</if>
                <if test="weight != null">#{weight},</if>
                <if test="stock != null">#{stock},</if>
                <if test="pointRedeemType != null">#{pointRedeemType},</if>
                <if test="productId != null">#{productId},</if>
                <if test="isUpdated != null">#{isUpdated},</if>
                <if test="belongStore != null">#{belongStore},</if>
        </trim>
    </insert>

    <update id="cleanProductSkus">
        truncate table product_skus
    </update>
</mapper>
