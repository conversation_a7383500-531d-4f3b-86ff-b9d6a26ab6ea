<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.mapper.ShelfCargoThingsMapper">
    <resultMap type="jp.co.nttdata.sz.web.entity.ShelfCargoThings" id="ShelfCargoThingsResult">
        <result property="id"    column="id"    />
        <result property="cargoThingId"    column="cargo_thing_id"    />
        <result property="cargoRoadId"    column="cargo_road_id"    />
        <result property="thingId"    column="thing_id"    />
        <result property="type"    column="type"    />
        <result property="thingName"    column="thing_name"    />
        <result property="stock"    column="stock"    />
        <result property="description"    column="description"    />
        <result property="extensionData"    column="extension_data"    />
        <result property="iconUrl"    column="icon_url"    />
        <result property="orderNumber"    column="order_number"    />
        <result property="thingOuterId"    column="thing_outer_id"    />
        <result property="isUpdated"    column="is_updated"    />
        <result property="belongStore"    column="belong_store"    />
        <result property="productId"    column="product_id"    />
        <result property="category"    column="category"    />
        <result property="cost"    column="cost"    />
    </resultMap>

    <sql id="selectShelfCargoThingsVo">
        select t1.id,
               t1.cargo_thing_id,
               t1.cargo_road_id,
               t1.thing_id,
               t1.type,
               t1.thing_name,
               t1.stock,
               t1.description,
               t1.extension_data,
               t1.icon_url,
               t1.order_number,
               t1.thing_outer_id,
               t1.is_updated,
               t1.belong_store,
               t2.product_id,
               t2.sales_volum as salesVolume,
               t3.category,
               t4.cost
        from shelf_cargo_things t1
        left join product_skus t2 on t1.thing_id = t2.id and t1.belong_store = t2.belong_store
        left join multiple_mst_products_category t3 on t2.product_id = t3.id and t2.belong_store = t3.store_id
        left join mst_products_cost t4 on t2.product_id = t4.id and t2.belong_store = t4.store_id
    </sql>

    <select id="selectShelfCargoThingsList" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoThings" resultMap="ShelfCargoThingsResult">
        <include refid="selectShelfCargoThingsVo"/>
        <where>
            <if test="cargoThingId != null "> and  t1.cargo_thing_id = #{cargoThingId}</if>
            <if test="cargoRoadId != null "> and  t1.cargo_road_id = #{cargoRoadId}</if>
            <if test="thingId != null "> and  t1.thing_id = #{thingId}</if>
            <if test="type != null  and type != ''"> and  t1.type = #{type}</if>
            <if test="thingName != null  and thingName != ''"> and  t1.thing_name like concat('%', #{thingName}, '%')</if>
            <if test="stock != null "> and  t1.stock = #{stock}</if>
            <if test="description != null  and description != ''"> and  t1.description = #{description}</if>
            <if test="extensionData != null  and extensionData != ''"> and  t1.extension_data = #{extensionData}</if>
            <if test="iconUrl != null  and iconUrl != ''"> and  t1.icon_url = #{iconUrl}</if>
            <if test="orderNumber != null "> and  t1.order_number = #{orderNumber}</if>
            <if test="thingOuterId != null  and thingOuterId != ''"> and  t1.thing_outer_id = #{thingOuterId}</if>
            <if test="isUpdated != null "> and  t1.is_updated = #{isUpdated}</if>
            <if test="belongStore != null  and belongStore != ''"> and  t1.belong_store = #{belongStore}</if>
        </where>
    </select>

    <resultMap type="jp.co.nttdata.sz.web.entity.CategoriesThings" id="CategoriesThingsListResult">
        <result property="category"    column="category"    />
        <result property="belongStore"    column="belong_store"    />
        <collection property="shelfCargoThingsList" ofType="jp.co.nttdata.sz.web.entity.ShelfCargoThings">
            <result property="id"    column="id"    />
            <result property="cargoThingId"    column="cargo_thing_id"    />
            <result property="cargoRoadId"    column="cargo_road_id"    />
            <result property="thingId"    column="thing_id"    />
            <result property="type"    column="type"    />
            <result property="thingName"    column="thing_name"    />
            <result property="stock"    column="stock"    />
            <result property="description"    column="description"    />
            <result property="extensionData"    column="extension_data"    />
            <result property="iconUrl"    column="icon_url"    />
            <result property="orderNumber"    column="order_number"    />
            <result property="thingOuterId"    column="thing_outer_id"    />
            <result property="isUpdated"    column="is_updated"    />
            <result property="productId"    column="product_id"    />
            <result property="cost"    column="cost"    />
        </collection>
    </resultMap>

    <select id="selectCategoriesThingsList" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoThings" resultMap="CategoriesThingsListResult">
        select t1.id,
        t1.cargo_thing_id,
        t1.cargo_road_id,
        t1.thing_id,
        t1.type,
        t1.thing_name,
        t1.stock,
        t1.description,
        t1.extension_data,
        t1.icon_url,
        t1.order_number,
        t1.thing_outer_id,
        t1.is_updated,
        t1.belong_store,
        t2.product_id,
        IFNULL( t3.category, '其他' ) as category,
        t4.cost
        from shelf_cargo_things t1
        left join product_skus t2 on t1.thing_id = t2.id and t1.belong_store = t2.belong_store
        left join multiple_mst_products_category t3 on t2.product_id = t3.id and t2.belong_store = t3.store_id
        left join mst_products_cost t4 on t2.product_id = t4.id and t2.belong_store = t4.store_id
        <where>
            <if test="cargoThingId != null "> and  t1.cargo_thing_id = #{cargoThingId}</if>
            <if test="cargoRoadId != null "> and  t1.cargo_road_id = #{cargoRoadId}</if>
            <if test="thingId != null "> and  t1.thing_id = #{thingId}</if>
            <if test="type != null  and type != ''"> and  t1.type = #{type}</if>
            <if test="thingName != null  and thingName != ''"> and  t1.thing_name like concat('%', #{thingName}, '%')</if>
            <if test="stock != null "> and  t1.stock = #{stock}</if>
            <if test="description != null  and description != ''"> and  t1.description = #{description}</if>
            <if test="extensionData != null  and extensionData != ''"> and  t1.extension_data = #{extensionData}</if>
            <if test="iconUrl != null  and iconUrl != ''"> and  t1.icon_url = #{iconUrl}</if>
            <if test="orderNumber != null "> and  t1.order_number = #{orderNumber}</if>
            <if test="thingOuterId != null  and thingOuterId != ''"> and  t1.thing_outer_id = #{thingOuterId}</if>
            <if test="isUpdated != null "> and  t1.is_updated = #{isUpdated}</if>
            <if test="belongStore != null  and belongStore != ''"> and  t1.belong_store = #{belongStore}</if>
        </where>
    </select>


    <select id="selectShelfCargoThings" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoThings" resultMap="ShelfCargoThingsResult">
        <include refid="selectShelfCargoThingsVo"/>
        where t1.id = #{id}
    </select>

    <insert id="insertShelfCargoThings" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoThings" useGeneratedKeys="true" keyProperty="id">
        insert into shelf_cargo_things
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cargoThingId != null">cargo_thing_id,</if>
            <if test="cargoRoadId != null">cargo_road_id,</if>
            <if test="thingId != null">thing_id,</if>
            <if test="type != null">type,</if>
            <if test="thingName != null">thing_name,</if>
            <if test="stock != null">stock,</if>
            <if test="description != null">description,</if>
            <if test="extensionData != null">extension_data,</if>
            <if test="iconUrl != null">icon_url,</if>
            <if test="orderNumber != null">order_number,</if>
            <if test="thingOuterId != null">thing_outer_id,</if>
            <if test="isUpdated != null">is_updated,</if>
            <if test="belongStore != null">belong_store,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cargoThingId != null">#{cargoThingId},</if>
            <if test="cargoRoadId != null">#{cargoRoadId},</if>
            <if test="thingId != null">#{thingId},</if>
            <if test="type != null">#{type},</if>
            <if test="thingName != null">#{thingName},</if>
            <if test="stock != null">#{stock},</if>
            <if test="description != null">#{description},</if>
            <if test="extensionData != null">#{extensionData},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="thingOuterId != null">#{thingOuterId},</if>
            <if test="isUpdated != null">#{isUpdated},</if>
            <if test="belongStore != null">#{belongStore},</if>
         </trim>
    </insert>


    <update id="updateShelfCargoThings" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoThings">
        update shelf_cargo_things
        <trim prefix="SET" suffixOverrides=",">
            <if test="cargoThingId != null">cargo_thing_id = #{cargoThingId},</if>
            <if test="cargoRoadId != null">cargo_road_id = #{cargoRoadId},</if>
            <if test="thingId != null">thing_id = #{thingId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="thingName != null">thing_name = #{thingName},</if>
            <if test="stock != null">stock = #{stock},</if>
            <if test="description != null">description = #{description},</if>
            <if test="extensionData != null">extension_data = #{extensionData},</if>
            <if test="iconUrl != null">icon_url = #{iconUrl},</if>
            <if test="orderNumber != null">order_number = #{orderNumber},</if>
            <if test="thingOuterId != null">thing_outer_id = #{thingOuterId},</if>
            <if test="isUpdated != null">is_updated = #{isUpdated},</if>
            <if test="belongStore != null">belong_store = #{belongStore},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteShelfCargoThings" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoThings">
        delete from shelf_cargo_things where id = #{id}
    </delete>

    <delete id="batchDeleteShelfCargoThings" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoThings">
        delete from shelf_cargo_things where id in
        <foreach item="item" collection="shelfCargoThingss" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </delete>
  </mapper>
