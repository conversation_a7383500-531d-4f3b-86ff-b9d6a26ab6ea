<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.QrCodeDao">
	<!-- <insert id="addQrCodeInfo" parameterType="jp.co.nttdata.sz.web.entity.QrCodeInfoEntity" 
		> SELECT simple_id AS simpleId, message AS message FROM tb_simple WHERE simple_id 
		= #{simpleId} </insert> -->

	<sql id="qr_code_columns">

		qrcode_id,
		qrcode_msg,
		create_time,
		is_active,
		limit_time,
		qrcode_coment
	</sql>

	<insert id="addQrCodeInfo"
		parameterType="jp.co.nttdata.sz.web.entity.QrCodeInfoEntity">
		INSERT INTO
		qr_code_info
		(
		<include refid="qr_code_columns" />
		)
		VAlUES
		(
		#{qrCodeId,jdbcType=VARCHAR},
		#{qrCodeMsg,jdbcType=VARCHAR},
		CURRENT_TIMESTAMP,
		#{isActive,jdbcType=VARCHAR},
		#{limitTime,jdbcType=VARCHAR},
		#{qrCodeComent,jdbcType=VARCHAR}
		)
	</insert>

	<insert id="insertQrCodeRecord"
		parameterType="jp.co.nttdata.sz.web.entity.QrCodeRecordEntity">
		INSERT INTO qr_code_record
		( user_id,
		token,
		create_time,
		expiry_time,
		save_path
		)VALUES(
		#{userId},
		#{token},
		#{createTime},
		#{expiryTime},
		#{savePath}
		)
	</insert>
	
	<delete id="delQrCodeByUserId" parameterType="java.lang.String">
	  DELETE FROM qr_code_record 
	  WHERE user_id =#{userId}
	</delete>

	<select id="getQrCodeRecord" parameterType="java.lang.String" resultType="jp.co.nttdata.sz.web.entity.QrCodeRecordEntity">
		SELECT
		id,
		token,
		create_time as createTime,
		expiry_time as expiryTime
		from
		qr_code_record
		where
		user_id =#{userId}
		<if test="token !=null">
			and token = #{token}
		</if>
	</select>


	<update id="updateQrCodeToken"  parameterType="jp.co.nttdata.sz.web.entity.QrCodeRecordEntity">
		UPDATE  qr_code_record
		set token =#{token},
		create_time =#{createTime},
		expiry_time =#{expiryTime}
		 where id =#{id}
	</update>
</mapper> 