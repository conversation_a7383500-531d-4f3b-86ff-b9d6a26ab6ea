<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.SiteMessageDao">
	<insert id="insertMessage"
		parameterType="jp.co.nttdata.sz.web.entity.SiteMessageInfoEntity">
		INSERT INTO site_msg_info
		(type,msg_title,content,send_time,send_user,receive_group <if test="storeId!=null and storeId!=''">,store_id</if>)
		VALUES
		(#{type},#{msgTitle},#{content},CURRENT_TIMESTAMP,#{sendUser},#{recevieGroup} <if test="storeId!=null and storeId!=''">,#{storeId}</if>)
	</insert>

	<select id="getMessageByUserId"
		resultType="jp.co.nttdata.sz.web.service.sitemsg.dto.SiteMsgWrapperDto">
		SELECT
		t1.msg_id AS msgId,
		t1.type AS type,
		t1.msg_title AS msgTitle,
		t1.content AS content,
		t1.send_time AS sendTime,
		t1.send_user AS sendUser,
		t1.receive_group AS receiveGroup,
		t3.user_name AS sendUserName
		FROM
		site_msg_info AS t1
		LEFT JOIN sys_group_user AS t2 ON t1.receive_group = t2.group_id
		LEFT JOIN sys_user AS t3 ON t1.send_user = t3.user_id
		WHERE
		t2.user_id = #{userId}
		<if test="storeId!=null and storeId!=''">
			AND t1.store_id = #{storeId}
		</if>
	</select>
</mapper> 
