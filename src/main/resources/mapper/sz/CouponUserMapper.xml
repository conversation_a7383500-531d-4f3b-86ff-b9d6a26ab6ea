<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="jp.co.nttdata.sz.web.dao.CouponUserDao">

    <resultMap id="BaseResultMap" type="jp.co.nttdata.sz.web.entity.CouponUserEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="coupon_id" property="couponId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="use_status" property="useStatus" jdbcType="INTEGER"/>
        <result column="use_time" property="useTime" jdbcType="TIMESTAMP"/>
        <result column="order_id" property="orderId" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, coupon_id, user_id, user_name, use_status, use_time, order_id, created_time, updated_time
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO mst_coupon_user (
        coupon_id, user_id, user_name, use_status
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.couponId}, #{item.userId}, #{item.userName}, #{item.useStatus})
        </foreach>
    </insert>

    <insert id="insert" parameterType="jp.co.nttdata.sz.web.entity.CouponUserEntity" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO mst_coupon_user (
            coupon_id, user_id, user_name, use_status
        ) VALUES (
            #{couponId}, #{userId}, #{userName}, #{useStatus}
        )
    </insert>

    <update id="update" parameterType="jp.co.nttdata.sz.web.entity.CouponUserEntity">
        UPDATE mst_coupon_user
        <set>
            <if test="useStatus != null">use_status = #{useStatus},</if>
            <if test="useTime != null">use_time = #{useTime},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM mst_coupon_user WHERE id = #{id}
    </delete>

    <delete id="deleteByCouponId" parameterType="java.lang.Long">
        DELETE FROM mst_coupon_user WHERE coupon_id = #{couponId}
    </delete>

    <select id="findById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mst_coupon_user
        WHERE id = #{id}
    </select>

    <select id="findByCouponIdAndUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mst_coupon_user
        WHERE coupon_id = #{couponId} AND user_id = #{userId}
    </select>

    <select id="findByCouponId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mst_coupon_user
        WHERE coupon_id = #{couponId}
        ORDER BY id ASC
    </select>

    <select id="findAvailableByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT cu.
        <include refid="Base_Column_List"/>
        FROM mst_coupon_user cu
        JOIN mst_coupon c ON cu.coupon_id = c.id
        WHERE cu.user_id = #{userId}
        AND cu.use_status = 0
        AND c.status = 1
        AND c.end_time > NOW()
        ORDER BY c.end_time ASC
    </select>

    <select id="findByCondition" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mst_coupon_user
        <where>
            <if test="couponId != null">
                AND coupon_id = #{couponId}
            </if>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="useStatus != null">
                AND use_status = #{useStatus}
            </if>
            <if test="orderId != null and orderId != ''">
                AND order_id = #{orderId}
            </if>
        </where>
        ORDER BY id DESC
        <if test="start != null and limit != null">
            LIMIT #{start}, #{limit}
        </if>
    </select>

    <select id="countByCondition" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT COUNT(*)
        FROM mst_coupon_user
        <where>
            <if test="couponId != null">
                AND coupon_id = #{couponId}
            </if>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="useStatus != null">
                AND use_status = #{useStatus}
            </if>
            <if test="orderId != null and orderId != ''">
                AND order_id = #{orderId}
            </if>
        </where>
    </select>

    <update id="updateUseStatus">
        UPDATE mst_coupon_user
        SET use_status = #{useStatus},
        use_time = NOW(),
        <if test="orderId != null">
            order_id = #{orderId},
        </if>
        updated_time = NOW()
        WHERE id = #{id}
    </update>

</mapper> 