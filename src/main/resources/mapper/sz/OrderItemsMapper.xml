<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.OrderItemsDao">

	<!-- mapping -->
	<!-- <resultMap id="OrderItems" type="org.thanos.platform.fw.auth.damain.OrderItems">
		<result property="id" column="id" jdbcType="VARCHAR" /> <result property="orderId"
		column="order_id" jdbcType="VARCHAR" /> <result property="productId" column="product_id"
		jdbcType="VARCHAR" /> <result property="productTitle" column="product_title"
		jdbcType="VARCHAR" /> <result property="quantity" column="quantity" jdbcType="VARCHAR"
		/> <result property="price" column="price" jdbcType="VARCHAR" /> <result
		property="picUrl" column="pic_url" jdbcType="VARCHAR" /> <result property="cargoRoadId"
		column="cargo_road_id" jdbcType="VARCHAR" /> <result property="updateTime"
		column="update_time" jdbcType="VARCHAR" /> <result property="createTime"
		column="create_time" jdbcType="VARCHAR" /> <result property="status" column="status"
		jdbcType="VARCHAR" /> </resultMap> -->

	<!-- <sql id="columns"> id, order_id, product_id, product_title, quantity,
		price, pic_url, cargo_road_id, update_time, create_time, status </sql> -->

	<sql id="columns"> id AS id, order_id AS orderId, product_id AS productId, product_title AS
		productTitle, quantity AS quantity, price AS price, pic_url AS picUrl, cargo_road_id AS
		cargoRoadId, update_time AS updateTime, create_time AS createTime, status AS status,
		prom_price AS promPrice, sku_id AS skuId </sql>

	<!-- 根据订单ID查询订单明细 -->
	<!-- <select id="getOrderItemByOrderId" resultMap="OrderItems"> -->
	<select id="getOrderItemByOrderId"
		resultType="jp.co.nttdata.sz.web.entity.OrderItem"> SELECT <include refid="columns" /> FROM
		order_items WHERE order_id = #{orderId} AND status = '1' </select>


	<!-- 插入一条订单明细 -->
	<insert id="insertOrderItem"
		parameterType="jp.co.nttdata.sz.web.entity.OrderItem"> INSERT INTO order_items
		(order_id,product_id,product_title,quantity,price,pic_url,cargo_road_id,update_time,create_time,status,sku_id,prom_price)
		VALUES
		(#{orderId},#{productId},#{productTitle},#{quantity},#{price},#{picUrl},#{cargoRoadId},CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,#{status},#{skuId},#{promPrice})
		</insert>

	<!-- 更新一条订单明细 -->
	<update id="updateOrderItem"
		parameterType="jp.co.nttdata.sz.web.entity.OrderItem"> UPDATE order_items SET
		product_id=#{productId}, product_title=#{productTitle}, quantity=#{quantity},
		price=#{price}, pic_url=#{picUrl}, cargo_road_id=#{cargoRoadId},
		update_time=CURRENT_TIMESTAMP WHERE id = #{id} </update>

	<delete id="delOrderItem" parameterType="java.lang.String"> DELETE FROM order_items WHERE
		id=#{itemId} </delete>

	<delete id="delOrderItemByOrderId"
		parameterType="java.lang.String"> DELETE FROM order_items WHERE order_id=#{orderId} </delete>

	<select id="countOrderItem" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		SELECT COUNT(*) FROM order_items WHERE id=#{id} </select>

	<select id="getOrderItemByItemId" parameterType="java.lang.Integer"
		resultType="jp.co.nttdata.sz.web.entity.OrderItem"> SELECT <include refid="columns" /> FROM
		order_items WHERE id = #{itemId} </select>
</mapper>