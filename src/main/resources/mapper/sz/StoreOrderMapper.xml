<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.dao.StoreOrderDao">
  <update id="updateEvaluateById" parameterType="jp.co.nttdata.sz.web.entity.StoreOrder">
    update mst_order
    <set>
      <if test="evaluatetionStar != null">
        evaluation_star = #{evaluatetionStar,jdbcType=INTEGER},
      </if>
      <if test="evaluationContent != null">
        order_evaluation = #{evaluationContent,jdbcType=VARCHAR},
      </if>
      <if test="evaluationTags != null">
        evaluation_tags = #{evaluationTags,jdbcType=VARCHAR},
      </if>
       <if test="evaluationImgs != null">
        evaluation_imgs = #{evaluationImgs,jdbcType=VARCHAR},
      </if>
    </set>
    ,evaluation_time = CURRENT_TIMESTAMP
    where order_id = #{orderId,jdbcType=VARCHAR}
  </update>

  <select id="queryCounts" parameterType="jp.co.nttdata.sz.web.entity.StoreOrder" resultType="INTEGER">
  	SELECT
	count( * )
		FROM
	mst_order
		WHERE
	order_id = #{orderId,jdbcType=VARCHAR}
  </select>

  <insert id="salesReturn" parameterType="jp.co.nttdata.sz.web.entity.SalesReturnInfoEntity">
  	INSERT INTO
  		mst_sales_return (id,sales_return_id, user_id, order_id, sales_retrun_status,return_amount,
  	 sales_return_operation_user, sales_return_create_time, sales_return_end_time, reason,tags,images,evaluationContent,returnContent)
		VALUES
	(0,#{returnId,jdbcType=VARCHAR},#{userId,jdbcType=VARCHAR},#{orderId,jdbcType=VARCHAR}
	,#{returnStatus,jdbcType=VARCHAR},#{returnAmount},#{operationUser,jdbcType=VARCHAR},CURRENT_TIMESTAMP,
	null,#{reason,jdbcType=VARCHAR},#{tags,jdbcType=VARCHAR},#{images,jdbcType=VARCHAR},#{evaluationContent,jdbcType=VARCHAR},#{returnContent,jdbcType=VARCHAR})
  </insert>


</mapper>
