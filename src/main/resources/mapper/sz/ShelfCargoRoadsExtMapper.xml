<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.mapper.ShelfCargoRoadsExtMapper">
    <resultMap type="jp.co.nttdata.sz.web.entity.ShelfCargoRoadsExt" id="ShelfCargoRoadsExtResult">
        <result property="deviceId"    column="device_id"    />
        <result property="cargoRoadId"    column="cargo_road_id"    />
        <result property="warningWeight"    column="warning_weight"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectShelfCargoRoadsExtVo">
        select device_id, cargo_road_id, warning_weight, create_time from shelf_cargo_roads_ext
    </sql>

    <select id="selectShelfCargoRoadsExtList" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoadsExt" resultMap="ShelfCargoRoadsExtResult">
        <include refid="selectShelfCargoRoadsExtVo"/>
        <where>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="cargoRoadId != null "> and cargo_road_id = #{cargoRoadId}</if>
            <if test="warningWeight != null "> and warning_weight = #{warningWeight}</if>
        </where>
    </select>

    <select id="selectShelfCargoRoadsExt" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoadsExt" resultMap="ShelfCargoRoadsExtResult">
        <include refid="selectShelfCargoRoadsExtVo"/>
        where device_id = #{deviceId} and cargo_road_id = #{cargoRoadId}
    </select>

    <insert id="insertShelfCargoRoadsExt" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoadsExt">
        insert into shelf_cargo_roads_ext
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="cargoRoadId != null">cargo_road_id,</if>
            <if test="warningWeight != null">warning_weight,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="cargoRoadId != null">#{cargoRoadId},</if>
            <if test="warningWeight != null">#{warningWeight},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>


    <update id="updateShelfCargoRoadsExt" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoadsExt">
        update shelf_cargo_roads_ext
        <trim prefix="SET" suffixOverrides=",">
            <if test="warningWeight != null">warning_weight = #{warningWeight},</if>
        </trim>
        where device_id = #{deviceId} and cargo_road_id = #{cargoRoadId}
    </update>


    <delete id="deleteShelfCargoRoadsExt" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoadsExt">
        delete from shelf_cargo_roads_ext where device_id = #{deviceId} and cargo_road_id = #{cargoRoadId}
    </delete>

    <delete id="batchDeleteShelfCargoRoadsExt" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoadsExt">
        delete from shelf_cargo_roads_ext where (device_id,cargo_road_id) in
        <foreach item="item" collection="shelfCargoRoadsExts" open="(" separator="," close=")">
            (#{item.deviceId},#{item.cargoRoadId})
        </foreach>
    </delete>
  </mapper>