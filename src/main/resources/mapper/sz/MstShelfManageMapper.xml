<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.mapper.MstShelfManageMapper">
    <resultMap type="jp.co.nttdata.sz.web.entity.MstShelf" id="MstShelfResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="mac"    column="mac"    />
        <result property="isLocked"    column="is_locked"    />
        <result property="deviceTypeName"    column="device_type_name"    />
        <result property="address"    column="address"    />
        <result property="description"    column="description"    />
        <result property="hardwareCode"    column="hardware_code"    />
        <result property="shutdownTime"    column="shutdown_time"    />
        <result property="shelfDirectionAngle"    column="shelf_direction_angle"    />
        <result property="resolutionLength"    column="resolution_length"    />
        <result property="startPointX"    column="start_point_x"    />
        <result property="resolutionWidth"    column="resolution_width"    />
        <result property="startPointY"    column="start_point_y"    />
        <result property="resolutionHeight"    column="resolution_height"    />
        <result property="subKey"    column="sub_key"    />
        <result property="os"    column="os"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="organizationUnitName"    column="organization_unit_name"    />
        <result property="origanizationUnitId"    column="origanization_unit_id"    />
        <result property="outType"    column="out_type"    />
        <result property="footerHeight"    column="footer_height"    />
        <result property="outerPartitionHeight"    column="outer_partition_height"    />
        <result property="layerPartitionHeight"    column="layer_partition_height"    />
        <result property="childDevices"    column="child_devices"    />
        <result property="belongStore"    column="belong_store"    />
        <result property="isUpdated"    column="is_updated"    />
        <result property="config"    column="config"    />
    </resultMap>

    <sql id="selectMstShelfVo">
        select id, name, mac, is_locked, device_type_name, address, description, hardware_code, shutdown_time, shelf_direction_angle, resolution_length, start_point_x, resolution_width, start_point_y, resolution_height, sub_key, os, tenant_name, tenant_id, organization_unit_name, origanization_unit_id, out_type, footer_height, outer_partition_height, layer_partition_height, child_devices, config, belong_store, is_updated from mst_shelf
    </sql>

    <select id="selectMstShelfList" parameterType="jp.co.nttdata.sz.web.entity.MstShelf" resultMap="MstShelfResult">
        <include refid="selectMstShelfVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="isLocked != null "> and is_locked = #{isLocked}</if>
            <if test="deviceTypeName != null  and deviceTypeName != ''"> and device_type_name like concat('%', #{deviceTypeName}, '%')</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="hardwareCode != null  and hardwareCode != ''"> and hardware_code = #{hardwareCode}</if>
            <if test="shutdownTime != null "> and shutdown_time = #{shutdownTime}</if>
            <if test="shelfDirectionAngle != null "> and shelf_direction_angle = #{shelfDirectionAngle}</if>
            <if test="resolutionLength != null "> and resolution_length = #{resolutionLength}</if>
            <if test="startPointX != null "> and start_point_x = #{startPointX}</if>
            <if test="resolutionWidth != null "> and resolution_width = #{resolutionWidth}</if>
            <if test="startPointY != null "> and start_point_y = #{startPointY}</if>
            <if test="resolutionHeight != null "> and resolution_height = #{resolutionHeight}</if>
            <if test="subKey != null  and subKey != ''"> and sub_key = #{subKey}</if>
            <if test="os != null  and os != ''"> and os = #{os}</if>
            <if test="tenantName != null  and tenantName != ''"> and tenant_name like concat('%', #{tenantName}, '%')</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            <if test="organizationUnitName != null  and organizationUnitName != ''"> and organization_unit_name like concat('%', #{organizationUnitName}, '%')</if>
            <if test="origanizationUnitId != null "> and origanization_unit_id = #{origanizationUnitId}</if>
            <if test="outType != null  and outType != ''"> and out_type = #{outType}</if>
            <if test="footerHeight != null "> and footer_height = #{footerHeight}</if>
            <if test="outerPartitionHeight != null "> and outer_partition_height = #{outerPartitionHeight}</if>
            <if test="layerPartitionHeight != null "> and layer_partition_height = #{layerPartitionHeight}</if>
            <if test="childDevices != null  and childDevices != ''"> and child_devices = #{childDevices}</if>
            <if test="belongStore != null  and belongStore != ''"> and belong_store = #{belongStore}</if>
            <if test="isUpdated != null "> and is_updated = #{isUpdated}</if>
            <if test="config != null and config != ''"> and config = #{config}</if>
        </where>
    </select>

    <select id="selectMstShelf" parameterType="jp.co.nttdata.sz.web.entity.MstShelf" resultMap="MstShelfResult">
        <include refid="selectMstShelfVo"/>
        where  id = #{id}
    </select>

    <insert id="insertMstShelf" parameterType="jp.co.nttdata.sz.web.entity.MstShelf" useGeneratedKeys="true" keyProperty="id">
        insert into mst_shelf
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="mac != null">mac,</if>
            <if test="isLocked != null">is_locked,</if>
            <if test="deviceTypeName != null">device_type_name,</if>
            <if test="address != null">address,</if>
            <if test="description != null">description,</if>
            <if test="hardwareCode != null">hardware_code,</if>
            <if test="shutdownTime != null">shutdown_time,</if>
            <if test="shelfDirectionAngle != null">shelf_direction_angle,</if>
            <if test="resolutionLength != null">resolution_length,</if>
            <if test="startPointX != null">start_point_x,</if>
            <if test="resolutionWidth != null">resolution_width,</if>
            <if test="startPointY != null">start_point_y,</if>
            <if test="resolutionHeight != null">resolution_height,</if>
            <if test="subKey != null">sub_key,</if>
            <if test="os != null">os,</if>
            <if test="tenantName != null">tenant_name,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="organizationUnitName != null">organization_unit_name,</if>
            <if test="origanizationUnitId != null">origanization_unit_id,</if>
            <if test="outType != null">out_type,</if>
            <if test="footerHeight != null">footer_height,</if>
            <if test="outerPartitionHeight != null">outer_partition_height,</if>
            <if test="layerPartitionHeight != null">layer_partition_height,</if>
            <if test="childDevices != null">child_devices,</if>
            <if test="belongStore != null and belongStore != ''">belong_store,</if>
            <if test="isUpdated != null">is_updated,</if>
            <if test="config != null">config,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="mac != null">#{mac},</if>
            <if test="isLocked != null">#{isLocked},</if>
            <if test="deviceTypeName != null">#{deviceTypeName},</if>
            <if test="address != null">#{address},</if>
            <if test="description != null">#{description},</if>
            <if test="hardwareCode != null">#{hardwareCode},</if>
            <if test="shutdownTime != null">#{shutdownTime},</if>
            <if test="shelfDirectionAngle != null">#{shelfDirectionAngle},</if>
            <if test="resolutionLength != null">#{resolutionLength},</if>
            <if test="startPointX != null">#{startPointX},</if>
            <if test="resolutionWidth != null">#{resolutionWidth},</if>
            <if test="startPointY != null">#{startPointY},</if>
            <if test="resolutionHeight != null">#{resolutionHeight},</if>
            <if test="subKey != null">#{subKey},</if>
            <if test="os != null">#{os},</if>
            <if test="tenantName != null">#{tenantName},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="organizationUnitName != null">#{organizationUnitName},</if>
            <if test="origanizationUnitId != null">#{origanizationUnitId},</if>
            <if test="outType != null">#{outType},</if>
            <if test="footerHeight != null">#{footerHeight},</if>
            <if test="outerPartitionHeight != null">#{outerPartitionHeight},</if>
            <if test="layerPartitionHeight != null">#{layerPartitionHeight},</if>
            <if test="childDevices != null">#{childDevices},</if>
            <if test="belongStore != null and belongStore != ''">#{belongStore},</if>
            <if test="isUpdated != null">#{isUpdated},</if>
            <if test="config != null">#{config},</if>
         </trim>
    </insert>


    <update id="updateMstShelf" parameterType="jp.co.nttdata.sz.web.entity.MstShelf">
        update mst_shelf
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="isLocked != null">is_locked = #{isLocked},</if>
            <if test="deviceTypeName != null">device_type_name = #{deviceTypeName},</if>
            <if test="address != null">address = #{address},</if>
            <if test="description != null">description = #{description},</if>
            <if test="hardwareCode != null">hardware_code = #{hardwareCode},</if>
            <if test="shutdownTime != null">shutdown_time = #{shutdownTime},</if>
            <if test="shelfDirectionAngle != null">shelf_direction_angle = #{shelfDirectionAngle},</if>
            <if test="resolutionLength != null">resolution_length = #{resolutionLength},</if>
            <if test="startPointX != null">start_point_x = #{startPointX},</if>
            <if test="resolutionWidth != null">resolution_width = #{resolutionWidth},</if>
            <if test="startPointY != null">start_point_y = #{startPointY},</if>
            <if test="resolutionHeight != null">resolution_height = #{resolutionHeight},</if>
            <if test="subKey != null">sub_key = #{subKey},</if>
            <if test="os != null">os = #{os},</if>
            <if test="tenantName != null">tenant_name = #{tenantName},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="organizationUnitName != null">organization_unit_name = #{organizationUnitName},</if>
            <if test="origanizationUnitId != null">origanization_unit_id = #{origanizationUnitId},</if>
            <if test="outType != null">out_type = #{outType},</if>
            <if test="footerHeight != null">footer_height = #{footerHeight},</if>
            <if test="outerPartitionHeight != null">outer_partition_height = #{outerPartitionHeight},</if>
            <if test="layerPartitionHeight != null">layer_partition_height = #{layerPartitionHeight},</if>
            <if test="childDevices != null">child_devices = #{childDevices},</if>
            <if test="belongStore != null and belongStore != ''">belong_store = #{belongStore},</if>
            <if test="isUpdated != null">is_updated = #{isUpdated},</if>
            <if test="config != null and config != ''"> config = #{config},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteMstShelf" parameterType="jp.co.nttdata.sz.web.entity.MstShelf">
        delete from mst_shelf where  id = #{id}
    </delete>

    <delete id="batchDeleteMstShelf" parameterType="jp.co.nttdata.sz.web.entity.MstShelf">
        delete from mst_shelf where id in
        <foreach item="item" collection="mstShelfs" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </delete>
  </mapper>