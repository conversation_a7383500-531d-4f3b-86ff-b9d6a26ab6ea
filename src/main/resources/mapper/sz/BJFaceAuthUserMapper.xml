<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.BJFaceAuthUserDao">

	<select id="getUserIdByAuthId" parameterType="java.lang.String"
		resultType="java.lang.String">
		SELECT user_id FROM bj_auth_user
		WHERE auth_id =#{authId}
	</select>

	<select id="findUserAuthed" parameterType="java.lang.String"
			resultType="java.lang.String">
		SELECT auth_id FROM bj_auth_user
		WHERE user_id =#{userId}
	</select>


	<insert id="insertAuthUser"
		parameterType="jp.co.nttdata.sz.web.entity.BJFaceAuthUserEntity">
		INSERT INTO bj_auth_user
		(user_id,auth_id)
		VALUES
		(#{userId},#{authId})
	</insert>

	<select id="getNextId" resultType="java.lang.String">
	 SELECT MAX(auth_id)+1  FROM bj_auth_user
	</select>
</mapper>
