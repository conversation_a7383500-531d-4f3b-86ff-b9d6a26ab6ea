<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.MemberInfoDao">

    <!-- 会员一览信息取得 -->
	<select id="findAll"
		parameterType="jp.co.nttdata.sz.web.entity.MemberInfoEntity"
		resultType="jp.co.nttdata.sz.web.entity.MemberInfoEntity">
		SELECT
		user_store_id AS userStoreId,
		store_user.user_id AS userId,
		sys_user.user_account AS userAccount,
		store_user.store_id AS storeId,
		flag AS flag,
		mst_store.store_name AS storeName,
		sys_user.user_name AS userName,
		sys_user.member_avatar AS memberAvatar,
		sys_user.face_img_path AS faceImgPath,
		qr_code_record.save_path AS qrcodePath,
		store_user.update_time AS updateTime,
		sys_user.staff_number AS staffNumber,
		sys_user.dept_id AS deptId
		FROM (store_user LEFT JOIN mst_store ON store_user.store_id =
		mst_store.store_id)
		LEFT JOIN sys_user ON store_user.user_id = sys_user.user_id
		LEFT JOIN  qr_code_record ON store_user.user_id = qr_code_record.user_id
		where
		1=1
		<if test="storeId !=null and storeId != ''">
			AND store_user.store_id = #{storeId}
		</if>
		<if test="userType !=null and userType != ''">
			AND sys_user.store_type = #{userType}
		</if>
		ORDER BY updateTime DESC
	</select>

    <!-- 会员信息检索-->
	<select id="search"
		parameterType="jp.co.nttdata.sz.web.entity.MemberInfoEntity"
		resultType="jp.co.nttdata.sz.web.entity.MemberInfoEntity">
		SELECT
		user_store_id AS userStoreId,
		store_user.user_id AS userId,
		sys_user.user_account AS userAccount,
		store_user.store_id AS storeId,
		flag AS flag,
		mst_store.store_name AS storeName,
		sys_user.user_name AS userName,
		sys_user.member_avatar AS memberAvatar,
		sys_user.face_img_path AS faceImgPath,
		sys_user.dept_id AS deptId,
		qr_code_record.save_path AS qrcodePath,
		store_user.update_time AS updateTime,
		sys_user.staff_number AS staffNumber
		FROM (store_user LEFT JOIN mst_store ON store_user.store_id =
		mst_store.store_id)
		LEFT JOIN sys_user ON store_user.user_id = sys_user.user_id
		LEFT JOIN  qr_code_record ON store_user.user_id = qr_code_record.user_id
		WHERE 1=1
		<if test="userId !=null and userId !=''">
			AND store_user.user_id = #{userId}
		</if>
		<if test="userType !=null and userType !=''">
			AND sys_user.user_type = #{userType}
		</if>
		<if test="userAccount !=null and userAccount !=''">
			AND sys_user.user_account like concat(concat('%',#{userAccount}),'%')
		</if>
		<if test="storeId !=null and storeId != ''">
			AND store_user.store_id = #{storeId}
		</if>
		<if test="flag !=null">
			AND store_user.flag = #{flag}
		</if>
		<if test="storeName !=null and  storeName !=''">
			AND mst_store.store_name = #{storeName}
		</if>
		<if test="userName !=null and userName != '' ">
			AND sys_user.user_name like concat(concat('%',#{userName}),'%')
		</if>
		<if test="deptId !=null and  deptId !=''">
			AND sys_user.dept_id = #{deptId}
		</if>
		ORDER BY updateTime DESC
	</select>

	<select id="searchAllMember"
			parameterType="jp.co.nttdata.sz.web.entity.MemberInfoEntity"
			resultType="jp.co.nttdata.sz.web.entity.MemberInfoEntity">
		SELECT
		NULL AS userStoreId,
		sys_user.user_id AS userId,
		sys_user.user_account AS userAccount,
		NULL AS storeId,
		NULL AS flag,
		NULL AS storeName,
		sys_user.user_name AS userName,
		sys_user.member_avatar AS memberAvatar,
		sys_user.face_img_path AS faceImgPath,
		sys_user.dept_id AS deptId,
		qr_code_record.save_path AS qrcodePath,
		sys_user.update_time AS updateTime,
		sys_user.staff_number AS staffNumber
		FROM
		sys_user
		LEFT JOIN qr_code_record ON sys_user.user_id = qr_code_record.user_id
		WHERE 1=1
		<if test="userId !=null and userId !=''">
			AND sys_user.user_id = #{userId}
		</if>
		<if test="userType !=null and userType !=''">
			AND sys_user.user_type = #{userType}
		</if>
		<if test="userAccount !=null and userAccount !=''">
			AND sys_user.user_account like concat(concat('%',#{userAccount}),'%')
		</if>
		<if test="userName !=null and userName != '' ">
			AND sys_user.user_name like concat(concat('%',#{userName}),'%')
		</if>
		<if test="deptId !=null and  deptId !=''">
			AND sys_user.dept_id = #{deptId}
		</if>
		ORDER BY updateTime DESC
	</select>

	 <!-- 会员信息检索-->
	<select id="searchByUserStoreId"
		resultType="jp.co.nttdata.sz.web.entity.MemberInfoEntity">
		SELECT
		user_store_id AS userStoreId,
		store_user.user_id AS userId,
		sys_user.user_account AS userAccount,
		store_user.store_id AS storeId,
		flag AS flag,
		mst_store.store_name AS storeName,
		sys_user.user_name AS userName,
		sys_user.member_avatar AS memberAvatar,
		sys_user.face_img_path AS faceImgPath,
		qr_code_record.save_path AS qrcodePath,
		sys_user.staff_number AS staffNumber,
		store_user.update_time AS updateTime
		FROM (store_user LEFT JOIN mst_store ON store_user.store_id =
		mst_store.store_id)
		LEFT JOIN sys_user ON store_user.user_id = sys_user.user_id
		LEFT JOIN  qr_code_record ON store_user.user_id = qr_code_record.user_id
		WHERE
			store_user.user_store_id=#{userStoreId}
		ORDER BY updateTime DESC
	</select>



    <!-- 会员审核-->
	<update id="checkMember">
		UPDATE store_user
		SET flag=1,
		update_time = CURRENT_TIMESTAMP
		WHERE user_store_id = #{userStoreId}
	</update>
</mapper>
