<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.mapper.MstProductsCostMapper">
    <resultMap type="jp.co.nttdata.sz.web.entity.MstProductsCost"
               id="MstProductsCostResult">
        <result property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="itemId" column="item_id"/>
        <result property="title" column="title"/>
        <result property="cost" column="cost"/>
    </resultMap>

    <sql id="selectMstProductsCostVo">
        select t1.id,
               t1.store_id,
               t1.item_id,
               t1.title,
               t1.cost
        from mst_products_cost t1
    </sql>

    <select id="selectMstProductsCostList"
            parameterType="jp.co.nttdata.sz.web.entity.MstProductsCost"
            resultMap="MstProductsCostResult">
        <include refid="selectMstProductsCostVo"/>
        <where>
            <if test="id != null ">and t1.id = #{id}</if>
            <if test="storeId != null  and storeId != ''">and t1.store_id =
                #{storeId}
            </if>
            <if test="itemId != null  and itemId != ''">and t1.item_id =
                #{itemId}
            </if>
            <if test="title != null  and title != ''">and t1.title like concat('%', #{title}, '%')

            </if>
            <if test="cost != null">and t1.cost like concat('%',
                #{cost}, '%')

            </if>
        </where>
    </select>

    <select id="selectMstProductsCost"
            parameterType="jp.co.nttdata.sz.web.entity.MstProductsCost"
            resultMap="MstProductsCostResult">
        <include refid="selectMstProductsCostVo"/>
        where t1.id = #{id}
    </select>

    <insert id="insertMstProductsCost"
            parameterType="jp.co.nttdata.sz.web.entity.MstProductsCost"
    >
        insert into mst_products_cost
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="storeId != null  and storeId != ''">store_id,</if>
            <if test="itemId != null  and itemId != ''">item_id,</if>
            <if test="title != null  and title != ''">title,</if>
            <if test="cost != null">cost,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="storeId != null  and storeId != ''">#{storeId},</if>
            <if test="itemId != null  and itemId != ''">#{itemId},</if>
            <if test="title != null  and title != ''">#{title},</if>
            <if test="cost != null">#{cost},</if>
        </trim>
    </insert>


    <update id="updateMstProductsCost"
            parameterType="jp.co.nttdata.sz.web.entity.MstProductsCost">
        update mst_products_cost
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null  and storeId != ''">store_id = #{storeId},</if>
            <if test="itemId != null  and itemId != ''">item_id = #{itemId},</if>
            <if test="title != null  and title != ''">title = #{title},</if>
            <if test="cost != null">cost = #{cost},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteMstProductsCost"
            parameterType="jp.co.nttdata.sz.web.entity.MstProductsCost">
        delete
        from mst_products_cost
        where id = #{id}
    </delete>

    <delete id="batchDeleteMstProductsCost"
            parameterType="jp.co.nttdata.sz.web.entity.MstProductsCost">
        delete from mst_products_cost where id in
        <foreach item="item" collection="mstProductsCosts" open="(" separator=","
                 close=")">
            #{item.id}
        </foreach>
    </delete>
</mapper>
