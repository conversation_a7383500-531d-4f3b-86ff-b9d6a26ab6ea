<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.dao.ShelfCargoRoadDao">

    <resultMap type="jp.co.nttdata.sz.web.entity.ShelfCargoRoad" id="ShelfCargoRoadResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="cargoType"    column="cargo_type"    />
        <result property="cargoRoadId"    column="cargo_road_id"    />
        <result property="startLength"    column="start_length"    />
        <result property="sensors"    column="sensors"    />
        <result property="grossWeight"    column="gross_weight"    />
        <result property="name"    column="name"    />
        <result property="outerId"    column="outer_id"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="stockCapacity"    column="stock_capacity"    />
        <result property="description"    column="description"    />
        <result property="extensionData"    column="extension_data"    />
        <result property="layerId"    column="layer_id"    />
        <result property="resolutionLength"    column="resolution_length"    />
        <result property="resolutionWidth"    column="resolution_width"    />
        <result property="startPointX"    column="start_point_x"    />
        <result property="resolutionHeight"    column="resolution_height"    />
        <result property="startPointZ"    column="start_point_z"    />
        <result property="spanRows"    column="span_rows"    />
        <result property="spanColums"    column="span_colums"    />
    </resultMap>

    <resultMap type="jp.co.nttdata.sz.web.service.shelf.Dto.LackShelfCargoRoadDto" id="LackShelfCargoRoadResult">
        <result property="roadName"  column="road_name"    />
        <result property="deviceName"  column="device_name"    />
        <result property="productName"  column="product_name"    />
        <result property="weight"    column="weight"    />
    </resultMap>

    <resultMap type="jp.co.nttdata.sz.web.controller.iosuser.dto.IosSuggestProductOutputDto" id="suggestProduct">
        <result property="id"  column="id"    />
        <result property="title"  column="title"    />
        <result property="price"  column="price"    />
        <result property="promPrice"  column="prom_price"    />
        <result property="saleCount"  column="sale_count"    />
        <result property="picUrl"  column="pic_url"    />
    </resultMap>

    <sql id="selectShelfCargoRoadVo">
        select id, device_id, cargo_type, cargo_road_id, start_length, sensors, gross_weight,name, outer_id, is_enabled, stock_capacity, description, extension_data, layer_id, resolution_length, resolution_width, start_point_x, resolution_height, start_point_z, span_rows, span_colums from shelf_cargo_roads
    </sql>

    <select id="selectShelfCargoRoadList" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoad" resultMap="ShelfCargoRoadResult">
        <include refid="selectShelfCargoRoadVo"/>
        <where>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="cargoType != null "> and cargo_type = #{cargoType}</if>
            <if test="cargoRoadId != null "> and cargo_road_id = #{cargoRoadId}</if>
            <if test="startLength != null "> and start_length = #{startLength}</if>
            <if test="sensors != null  and sensors != ''"> and sensors = #{sensors}</if>
            <if test="grossWeight != null "> and gross_weight = #{grossWeight}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="outerId != null  and outerId != ''"> and outer_id = #{outerId}</if>
            <if test="isEnabled != null  and isEnabled != ''"> and is_enabled = #{isEnabled}</if>
            <if test="stockCapacity != null "> and stock_capacity = #{stockCapacity}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="extensionData != null  and extensionData != ''"> and extension_data = #{extensionData}</if>
            <if test="layerId != null "> and layer_id = #{layerId}</if>
            <if test="resolutionLength != null "> and resolution_length = #{resolutionLength}</if>
            <if test="resolutionWidth != null "> and resolution_width = #{resolutionWidth}</if>
            <if test="startPointX != null "> and start_point_x = #{startPointX}</if>
            <if test="resolutionHeight != null "> and resolution_height = #{resolutionHeight}</if>
            <if test="startPointZ != null "> and start_point_z = #{startPointZ}</if>
            <if test="spanRows != null "> and span_rows = #{spanRows}</if>
            <if test="spanColums != null "> and span_colums = #{spanColums}</if>
        </where>
    </select>

    <select id="selectShelfCargoRoadById" parameterType="Integer" resultMap="ShelfCargoRoadResult">
        <include refid="selectShelfCargoRoadVo"/>
        where id = #{id}
    </select>

    <insert id="insertShelfCargoRoad" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoad">
        insert into shelf_cargo_roads
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="cargoType != null">cargo_type,</if>
            <if test="cargoRoadId != null">cargo_road_id,</if>
            <if test="startLength != null">start_length,</if>
            <if test="sensors != null">sensors,</if>
            <if test="grossWeight != null">gross_weight,</if>
            <if test="name != null">name,</if>
            <if test="outerId != null">outer_id,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="stockCapacity != null">stock_capacity,</if>
            <if test="description != null">description,</if>
            <if test="extensionData != null">extension_data,</if>
            <if test="layerId != null">layer_id,</if>
            <if test="resolutionLength != null">resolution_length,</if>
            <if test="resolutionWidth != null">resolution_width,</if>
            <if test="startPointX != null">start_point_x,</if>
            <if test="resolutionHeight != null">resolution_height,</if>
            <if test="startPointZ != null">start_point_z,</if>
            <if test="spanRows != null">span_rows,</if>
            <if test="spanColums != null">span_colums,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="cargoType != null">#{cargoType},</if>
            <if test="cargoRoadId != null">#{cargoRoadId},</if>
            <if test="startLength != null">#{startLength},</if>
            <if test="sensors != null">#{sensors},</if>
            <if test="grossWeight != null">#{grossWeight},</if>
            <if test="weight != null">#{weight},</if>
            <if test="selfWeight != null">#{selfWeight},</if>
            <if test="warningWeight != null">#{warningWeight},</if>
            <if test="name != null">#{name},</if>
            <if test="outerId != null">#{outerId},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="stockCapacity != null">#{stockCapacity},</if>
            <if test="description != null">#{description},</if>
            <if test="extensionData != null">#{extensionData},</if>
            <if test="layerId != null">#{layerId},</if>
            <if test="resolutionLength != null">#{resolutionLength},</if>
            <if test="resolutionWidth != null">#{resolutionWidth},</if>
            <if test="startPointX != null">#{startPointX},</if>
            <if test="resolutionHeight != null">#{resolutionHeight},</if>
            <if test="startPointZ != null">#{startPointZ},</if>
            <if test="spanRows != null">#{spanRows},</if>
            <if test="spanColums != null">#{spanColums},</if>
         </trim>
    </insert>

    <update id="updateShelfCargoRoad" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoad">
        update shelf_cargo_roads
        <trim prefix="SET" suffixOverrides=",">
            device_id = #{deviceId},
            cargo_type = #{cargoType},
            cargo_road_id = #{cargoRoadId},
            start_length = #{startLength},
            sensors = #{sensors},
            gross_weight = #{grossWeight},
            name = #{name},
            outer_id = #{outerId},
            is_enabled = #{isEnabled},
            stock_capacity = #{stockCapacity},
            description = #{description},
            extension_data = #{extensionData},
            <if test="layerId != null">layer_id = #{layerId},</if>
            resolution_length = #{resolutionLength},
            resolution_width = #{resolutionWidth},
            start_point_x = #{startPointX},
            resolution_height = #{resolutionHeight},
            start_point_z = #{startPointZ},
            span_rows = #{spanRows},
            span_colums = #{spanColums}
        </trim>
        where id = #{id}
    </update>

    <update id="updateShelfWeight" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoad">
        update shelf_cargo_roads
        <trim prefix="SET" suffixOverrides=",">
            gross_weight = #{grossWeight}
        </trim>
        where cargo_road_id = #{cargoRoadId}
    </update>

    <delete id="deleteShelfCargoRoadById" parameterType="Integer">
        delete from shelf_cargo_roads where id = #{id}
    </delete>

    <delete id="deleteShelfCargoRoadByIds" parameterType="String">
        delete from shelf_cargo_roads where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectLackShelfCargoRoad" resultMap="LackShelfCargoRoadResult">
        SELECT
        t1.NAME AS road_name,
        t2.NAME AS device_name,
        t3.thing_name AS product_name,
        IFNULL( gross_weight, 0 ) AS weight
        FROM
        shelf_cargo_roads t1
        LEFT JOIN mst_shelf t2 ON t1.device_id = t2.id
        LEFT JOIN shelf_cargo_things t3 ON t3.cargo_road_id = t1.id
        LEFT JOIN shelf_cargo_roads_ext t4 ON t4.cargo_road_id = t1.id
        WHERE
        t4.warning_weight IS NOT NULL
        AND IFNULL( gross_weight, 0 ) &lt;= IFNULL( t4.warning_weight, 0 )
    </select>


    <select id="selectSuggestProductList" resultMap="suggestProduct">
        SELECT
        t4.id,
        t4.title,
        t4.price,
        t4.prom_pice as prom_price,
        t4.pic_url,
        count( * ) AS sale_count
        FROM
        (
        SELECT DISTINCT
        ( t2.thing_id ) AS psku_id,
        t3.*
        FROM
        shelf_cargo_things t2
        LEFT JOIN product_skus t3 ON t2.thing_id = t3.id
        ) t4
        RIGHT JOIN         ( SELECT t2.* FROM mst_order t1 INNER JOIN order_items t2 ON t1.order_id = t2.order_id
                             WHERE t1.consumer_store_id = #{storeId} ) t1 ON t1.sku_id = t4.psku_id
        WHERE
        t4.sku_id IS NOT NULL
        GROUP BY
            t4.id,
            t4.title,
            t4.price,
            t4.prom_pice,
            t4.pic_url
        ORDER BY
        sale_count DESC
        limit #{size}
    </select>

</mapper>
