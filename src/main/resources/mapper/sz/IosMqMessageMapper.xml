<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.dao.IosMqMessageDao">
  <resultMap id="BaseResultMap" type="jp.co.nttdata.sz.web.entity.IosMqMessageEntity">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="message_type" jdbcType="VARCHAR" property="messageType" />
    <result column="message_content" jdbcType="VARCHAR" property="messageContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, message_id, user_id, message_type, message_content, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ios_mq_message
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByMessageId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ios_mq_message
    where message_id = #{messageId,jdbcType=VARCHAR}
  </select>
  
  <select id="selectByUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ios_mq_message
    where user_id = #{userId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ios_mq_message
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="jp.co.nttdata.sz.web.entity.IosMqMessageEntity">
    insert into ios_mq_message ( message_id, user_id, 
      message_type, message_content, create_time
      )
    values (#{messageId,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, 
      #{messageType,jdbcType=VARCHAR}, #{messageContent,jdbcType=VARCHAR},CURRENT_TIMESTAMP
      )
  </insert>
  <insert id="insertSelective" parameterType="jp.co.nttdata.sz.web.entity.IosMqMessageEntity">
    insert into ios_mq_message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="messageType != null">
        message_type,
      </if>
      <if test="messageContent != null">
        message_content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="messageType != null">
        #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="messageContent != null">
        #{messageContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="jp.co.nttdata.sz.web.entity.IosMqMessageEntity">
    update ios_mq_message
    <set>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="messageType != null">
        message_type = #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="messageContent != null">
        message_content = #{messageContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="jp.co.nttdata.sz.web.entity.IosMqMessageEntity">
    update ios_mq_message
    set message_id = #{messageId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      message_type = #{messageType,jdbcType=VARCHAR},
      message_content = #{messageContent,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>