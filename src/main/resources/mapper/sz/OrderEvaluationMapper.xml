<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.OrderEvaluationDao">

	<!-- 评价一览信息取得 -->
	<select id="searchEvaluation"
		parameterType="jp.co.nttdata.sz.web.entity.OrderEvaluation"
		resultType="jp.co.nttdata.sz.web.entity.OrderEvaluation">
		SELECT
			t1.order_id AS orderId,
			t1.order_evaluation AS orderEvaluation,
			t1.evaluation_star AS evaluationStar,
			t1.create_time AS createTime,
			t2.store_id AS storeId,
			t2.store_name AS storeName,
			t3.user_id AS userId,
			t3.user_account AS userAccount,
			t3.user_name AS userName,
			t1.evaluation_time AS evaluationTime 
		FROM
			mst_order AS t1
			LEFT JOIN mst_store AS t2 ON t1.consumer_store_id = t2.store_id
			LEFT JOIN sys_user AS t3 ON t1.user_id = t3.user_id 
		WHERE
			IFNULL(t1.evaluation_star,-1)>=0
		<if test="userId != null  and userId != ''">
			AND user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND user_name like concat(concat('%',#{userName}),'%')
		</if>
		<if test="userAccount != null and userAccount != ''">
			AND user_account like
			concat(concat('%',#{userAccount}),'%')
		</if>
		<if test="orderId != null and orderId != ''">
			AND order_id = #{orderId}
		</if>
		<if test="storeId != null and storeId != ''">
			AND consumer_store_id = #{storeId}
		</if>
		<if test="storeName != null  and storeName != ''">
			AND store_name = #{storeName}
		</if>
		<if test="evaluationTime != null">
			AND DATE_FORMAT(evaluation_time,'%Y-%m-%d') = DATE_FORMAT(#{evaluationTime},'%Y-%m-%d')
		</if>
		ORDER BY evaluation_time DESC	
	</select>
	
	<select id="getOrderEvaluationByOrderId" parameterType="java.lang.String" 
	resultType="jp.co.nttdata.sz.web.controller.evaluation.dto.EvaluationOrderInfo">
			SELECT
			t1.id AS id,
			t1.order_id AS orderId,
			t1.amount AS amount,
			t1.quantity AS quantity,
			t1.order_evaluation AS orderEvaluation,
			t1.evaluation_star AS evaluationStar,
			t1.evaluation_tags AS evaluationTags,
			t1.evaluation_imgs AS evaluationImgs,
			t1.settlement_type AS settlementType,
			t1.order_status AS orderStatus,
			t1.operation_user AS operationUser,
			t1.create_time AS createTime,
			t1.shopcar_id AS shopcarId,
			t3.user_id AS userId,
			t3.user_phone AS userPhone,
			t3.user_account AS userAccount,
			t3.user_name AS userName,
			t3.member_avatar AS memberAvatar,
			t1.evaluation_time AS evaluationTime 
		FROM
			mst_order AS t1
			LEFT JOIN sys_user AS t3 ON t1.user_id = t3.user_id 
		WHERE t1.order_id =#{orderId}	
	</select>

</mapper> 