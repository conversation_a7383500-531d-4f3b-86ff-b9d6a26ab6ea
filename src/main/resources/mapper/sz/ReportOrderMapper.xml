<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.ReportOrderDao">


    <select id="orderSaleAmountDay"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        t1.date,
        t1.amount,
        IF
        ( t2.cost IS NULL, NULL, ROUND( t2.cost * 0.8, 2 ) ) AS productCost,
        IF
        ( t2.cost IS NULL, NULL, ROUND( t2.cost * 0.2, 2 ) ) AS moCost
        FROM
        (
        SELECT
        DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) AS date,
        SUM( t1.amount ) AS amount
        FROM
        mst_order t1
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) >= DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 29 DAY ), '%Y-%m-%d' )
        AND t1.consumer_store_id = #{storeId}
        GROUP BY
        DATE_FORMAT( t1.create_time, '%Y-%m-%d' )
        ) t1
        LEFT JOIN (
        SELECT
        t1.`month` AS date,
        SUM( t1.cost ) / DAY ( LAST_DAY( CONCAT( t1.`month`, '-01' ) ) ) AS cost
        FROM
        mst_cost t1
        WHERE
        t1.store_id = #{storeId}
        AND t1.`month` >= DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 29 DAY ), '%Y-%m' )
        AND <![CDATA[ t1.`month` <= DATE_FORMAT( NOW( ), '%Y-%m' )]]>
        GROUP BY
        t1.`month`
        ) t2 ON DATE_FORMAT( t1.date, '%Y-%m' ) = t2.date
    </select>


    <select id="orderSalePredictionHour"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        o1.date,
        ROUND(IFNULL( o1.amountPer, 0),2) AS amountPer,
        ROUND(IFNULL( o2.amount, 0 ),2) AS amount,
        IF
        ( DATE_FORMAT( DATE_ADD( NOW( ), INTERVAL 8 HOUR ), '%H' )  + 0 > o1.date, 1, 0 ) AS flag
        FROM
        (
        WITH RECURSIVE hour_day AS ( SELECT 0 AS date UNION ALL SELECT date + 1 FROM hour_day WHERE <![CDATA[ date < 23 ]]>) SELECT
        t1.date,
        IFNULL( t2.amountPer, 0 ) AS amountPer
        FROM
        hour_day t1
        LEFT JOIN (
        SELECT
        DATE_FORMAT( t1.create_time, '%H' ) AS date,
        SUM( t1.amount ) / count( DISTINCT DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) ) AS amountPer
        FROM
        mst_order t1
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) >= DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 6 DAY ), '%Y-%m-%d' )
        AND <![CDATA[  DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) < DATE_FORMAT( NOW( ), '%Y-%m-%d' )]]>
        AND t1.consumer_store_id = #{storeId}
        GROUP BY
        date
        ) t2 ON t1.date = t2.date
        ) o1
        LEFT JOIN (
        SELECT
        DATE_FORMAT( t1.create_time, '%H' ) AS date,
        SUM( t1.amount ) AS amount
        FROM
        mst_order t1
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) = DATE_FORMAT( NOW( ), '%Y-%m-%d' )
        AND t1.consumer_store_id =  #{storeId}
        GROUP BY
        date
        ) o2 ON o1.date = o2.date
    </select>


    <select id="orderNumberDay"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        t1.date,
        t1.`status`,
        IFNULL( t2.count, 0 ) AS count
        FROM
        (
        SELECT
        i1.date,
        i2.`status`
        FROM
        (
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 0 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 1 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 2 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 3 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 4 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 5 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 6 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 7 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 8 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 9 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 10 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 11 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 12 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 13 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 14 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 15 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 16 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 17 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 18 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 19 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 20 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 21 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 22 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 23 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 24 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 25 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 26 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 27 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 28 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 29 DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 30 DAY ), '%Y-%m-%d' ) AS date
        ) i1
        LEFT JOIN mst_holidy i2 ON i1.date = i2.date
        ) t1
        LEFT JOIN (
        SELECT
        DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) AS date,
        count( 1 ) AS count
        FROM
        mst_order t1
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) >= DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 30 DAY ), '%Y-%m-%d' )
        AND t1.consumer_store_id =  #{storeId}
        GROUP BY
        DATE_FORMAT( t1.create_time, '%Y-%m-%d' )
        ) t2 ON t1.date = t2.date
    </select>

    <select id="orderSaleAmountWeek"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        t1.date,
        ROUND( t1.amount, 2 ) AS amount,
        IF
        ( t2.cost IS NULL, NULL, ROUND( t2.cost * 0.8, 2 ) ) AS productCost,
        IF
        ( t2.cost IS NULL, NULL, ROUND( t2.cost * 0.2, 2 ) ) AS moCost
        FROM
        (
        SELECT
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' ) AS date,
        SUM( t1.amount ) AS amount
        FROM
        mst_order t1
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) >= DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 60 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 60 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        )
        AND t1.consumer_store_id = #{storeId}
        GROUP BY
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' )
        ) t1
        LEFT JOIN (
        SELECT
        t1.`month` AS date,
        ( SUM( t1.cost ) / DAY ( LAST_DAY( CONCAT( t1.`month`, '-01' ) ) ) ) * 7 AS cost
        FROM
        mst_cost t1
        WHERE
        t1.store_id = #{storeId}
        AND t1.`month` >= DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 60 DAY ), '%Y-%m' )
        AND <![CDATA[  t1.`month` <= DATE_FORMAT( NOW( ), '%Y-%m' )]]>
        GROUP BY
        t1.`month`
        ) t2 ON DATE_FORMAT( t1.date, '%Y-%m' ) = t2.date;
    </select>

    <select id="orderSalePredictionWeek"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        t3.date,
        t3.amount,
        t3.rank1,
        IF
        (
        t3.rank1 IS NULL,
        ( SUM( IF ( t3.rank1 > 4, t3.prediction, 0 ) ) over ( ) / 3 ) - ( 100 * rand( ) ),
        t3.prediction
        ) AS prediction
        FROM
        (
        SELECT
        t2.date,
        t2.amount,
        t2.rank1,
        (
        SUM( t2.amount * t2.rank1 ) over ( ORDER BY t2.date ASC ) - ( t2.amount * t2.rank1 )
        ) / ( SUM( t2.rank1 ) over ( ORDER BY t2.date ASC ) - t2.rank1 ) AS prediction
        FROM
        (
        SELECT
        t1.*,
        row_number ( ) over ( ORDER BY date ASC ) AS rank1
        FROM
        (
        SELECT
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' ) AS date,
        SUM( t1.amount ) AS amount
        FROM
        mst_order t1
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) >= DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 40 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 40 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        )
        AND t1.consumer_store_id = #{storeId}
        GROUP BY
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' )
        ) t1
        ) t2 UNION ALL
        SELECT
        DATE_FORMAT(
        DATE_SUB(
        DATE_ADD( NOW( ), INTERVAL 7 DAY ),
        INTERVAL WEEKDAY( DATE_ADD( NOW( ), INTERVAL 7 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        ) AS date,
        NULL AS amount,
        NULL AS rank1,
        NULL AS prediction UNION ALL
        SELECT
        DATE_FORMAT(
        DATE_SUB(
        DATE_ADD( NOW( ), INTERVAL 14 DAY ),
        INTERVAL WEEKDAY( DATE_ADD( NOW( ), INTERVAL 14 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        ) AS date,
        NULL AS amount,
        NULL AS rank1,
        NULL AS prediction
        ) t3
    </select>

    <select id="orderNumberWeek"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        t1.date,
        1 AS `status`,
        IFNULL( t2.count, 0 ) AS count
        FROM
        (
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL WEEKDAY( NOW( ) ) DAY ), '%Y-%m-%d' ) AS date UNION ALL
        SELECT
        DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 7 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 7 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        ) AS date UNION ALL
        SELECT
        DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 14 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 14 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        ) AS date UNION ALL
        SELECT
        DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 21 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 21 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        ) AS date UNION ALL
        SELECT
        DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 28 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 28 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        ) AS date UNION ALL
        SELECT
        DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 35 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 35 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        ) AS date UNION ALL
        SELECT
        DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 42 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 42 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        ) AS date UNION ALL
        SELECT
        DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 49 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 49 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        ) AS date UNION ALL
        SELECT
        DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 56 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 56 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        ) AS date
        ) t1
        LEFT JOIN (
        SELECT
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' ) AS date,
        count( 1 ) AS count
        FROM
        mst_order t1
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) >= DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 56 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 56 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        )
        AND t1.consumer_store_id = #{storeId}
        GROUP BY
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' )
        ) t2 ON t1.date = t2.date
    </select>

    <select id="orderSaleAmountMonth"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        t1.date,
        t1.amount,
        IF
        ( t2.cost IS NULL, NULL, ROUND( t2.cost * 0.8, 2 ) ) AS productCost,
        IF
        ( t2.cost IS NULL, NULL, ROUND( t2.cost * 0.2, 2 ) ) AS moCost
        FROM
        (
        SELECT
        DATE_FORMAT( t1.create_time, '%Y-%m' ) AS date,
        SUM( t1.amount ) AS amount
        FROM
        mst_order t1
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND DATE_FORMAT( t1.create_time, '%Y-%m' ) >= DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 11 MONTH ), '%Y-%m' )
        AND t1.consumer_store_id = #{storeId}
        GROUP BY
        DATE_FORMAT( t1.create_time, '%Y-%m' )
        ) t1
        LEFT JOIN (
        SELECT
        t1.`month` AS date,
        IF
        (
        t1.`month` = DATE_FORMAT( NOW( ), '%Y-%m' ),
        t1.cost / DAY ( LAST_DAY( CONCAT( t1.`month`, '-01' ) ) ) * DAY ( NOW( ) ),
        t1.cost
        ) AS cost
        FROM
        mst_cost t1
        WHERE
        t1.store_id = #{storeId}
        AND t1.`month` >= DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 11 MONTH ), '%Y-%m' )
        AND <![CDATA[  t1.`month` <= DATE_FORMAT( NOW( ), '%Y-%m' )]]>
        ) t2 ON t1.date = t2.date;
    </select>

    <select id="orderNumberMonth"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        t1.date,
        1 AS `status`,
        IFNULL( t2.count, 0 ) AS count
        FROM
        (
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 0 DAY ), '%Y-%m' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 1 MONTH ), '%Y-%m' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 2 MONTH ), '%Y-%m' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 3 MONTH ), '%Y-%m' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 4 MONTH ), '%Y-%m' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 5 MONTH ), '%Y-%m' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 6 MONTH ), '%Y-%m' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 7 MONTH ), '%Y-%m' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 8 MONTH ), '%Y-%m' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 9 MONTH ), '%Y-%m' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 10 MONTH ), '%Y-%m' ) AS date UNION ALL
        SELECT
        DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 11 MONTH ), '%Y-%m' ) AS date
        ) t1
        LEFT JOIN (
        SELECT
        DATE_FORMAT( t1.create_time, '%Y-%m' ) AS date,
        count( 1 ) AS count
        FROM
        mst_order t1
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND DATE_FORMAT( t1.create_time, '%Y-%m' ) >= DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 11 MONTH ), '%Y-%m' )
        AND t1.consumer_store_id = #{storeId}
        GROUP BY
        DATE_FORMAT( t1.create_time, '%Y-%m' )
        ) t2 ON t1.date = t2.date
    </select>

    <select id="orderSaleCategory"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        t1.category,
        t1.quantity,
        CONCAT( ROUND( ( t1.quantity - t2.quantity ) / t2.quantity * 100 ), '%' ) AS qoq
        FROM
        (
        SELECT
        t4.category,
        SUM( t3.quantity ) AS quantity
        FROM
        mst_order t1
        LEFT JOIN order_items t3 ON t3.order_id = t1.order_id
        LEFT JOIN mst_products_category t4 ON t4.id = t3.product_id
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND
        CASE
        #{type}
        WHEN 'day' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) = DATE_FORMAT( NOW( ), '%Y-%m-%d' )
        WHEN 'week' THEN
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' ) = DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL WEEKDAY( NOW( ) ) DAY ), '%Y-%m-%d' )
        WHEN 'month' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m' ) = DATE_FORMAT( NOW( ), '%Y-%m' ) ELSE 0
        END
        AND t3.product_id IS NOT NULL
        GROUP BY
        t4.category
        ORDER BY
        quantity DESC
        ) t1
        LEFT JOIN (
        SELECT
        t4.category,
        CASE
        #{type}
        WHEN 'day' THEN
        SUM( t3.quantity )
        WHEN 'week' THEN
        SUM( t3.quantity ) / 7 * ( WEEKDAY ( NOW( ) ) + 1 )
        WHEN 'month' THEN
        SUM( t3.quantity ) / DAY ( LAST_DAY( CONCAT( DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 1 MONTH ), '%Y-%m' ), '-01' ) ) ) * DAY ( NOW( ) ) ELSE 0
        END AS quantity
        FROM
        mst_order t1
        LEFT JOIN order_items t3 ON t3.order_id = t1.order_id
        LEFT JOIN mst_products_category t4 ON t4.id = t3.product_id
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND
        CASE
        #{type}
        WHEN 'day' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) = DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 1 DAY ), '%Y-%m-%d' )
        WHEN 'week' THEN
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' ) = DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 7 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 7 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        )
        WHEN 'month' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m' ) = DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 1 MONTH ), '%Y-%m' ) ELSE 0
        END
        AND t3.product_id IS NOT NULL
        GROUP BY
        t4.category
        ORDER BY
        quantity DESC
        ) t2 ON t1.category = t2.category
    </select>

    <select id="orderSaleCategoryProduct"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        t1.product_id,
        t1.product_title,
        t1.quantity,
        CONCAT( ROUND( ( t1.quantity - t2.quantity ) / t2.quantity * 100 ), '%' ) AS qoq
        FROM
        (
        SELECT
        t3.product_id,
        t3.product_title,
        SUM( t3.quantity ) AS quantity
        FROM
        mst_order t1
        LEFT JOIN order_items t3 ON t3.order_id = t1.order_id
        LEFT JOIN multiple_mst_products_category t4 ON t4.id = t3.product_id  AND t4.store_id = #{storeId}
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND
        CASE
        #{type}
        WHEN 'day' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) = DATE_FORMAT( NOW( ), '%Y-%m-%d' )
        WHEN 'week' THEN
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' ) = DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL WEEKDAY( NOW( ) ) DAY ), '%Y-%m-%d' )
        WHEN 'month' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m' ) = DATE_FORMAT( NOW( ), '%Y-%m' ) ELSE 0
        END
        AND t3.product_id IS NOT NULL
        AND t4.category = #{category}
        AND t1.consumer_store_id = #{storeId}

        GROUP BY
        t3.product_id,
        t3.product_title
        ORDER BY
        quantity DESC
        ) t1
        LEFT JOIN (
        SELECT
        t3.product_id,
        t3.product_title,
        CASE
        #{type}
        WHEN 'day' THEN
        SUM( t3.quantity )
        WHEN 'week' THEN
        SUM( t3.quantity ) / 7 * ( WEEKDAY ( NOW( ) ) + 1 )
        WHEN 'month' THEN
        SUM( t3.quantity ) / DAY ( LAST_DAY( CONCAT( DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 1 MONTH ), '%Y-%m' ), '-01' ) ) ) * DAY ( NOW( ) ) ELSE 0
        END AS quantity
        FROM
        mst_order t1
        LEFT JOIN order_items t3 ON t3.order_id = t1.order_id
        LEFT JOIN multiple_mst_products_category t4 ON t4.id = t3.product_id AND t4.store_id = #{storeId}
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND
        CASE
        #{type}
        WHEN 'day' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) = DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 1 DAY ), '%Y-%m-%d' )
        WHEN 'week' THEN
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' ) = DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 7 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 7 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        )
        WHEN 'month' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m' ) = DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 1 MONTH ), '%Y-%m' ) ELSE 0
        END
        AND t3.product_id IS NOT NULL
        AND t4.category = #{category}
        AND t1.consumer_store_id = #{storeId}

        GROUP BY
        t3.product_id,
        t3.product_title
        ORDER BY
        quantity DESC
        ) t2 ON t1.product_id = t2.product_id
        AND t1.product_title = t2.product_title
    </select>

    <select id="orderSaleCategoryProductSex"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        t1.sex,
        t1.quantity,
        CONCAT( ROUND( ( t1.quantity - t2.quantity ) / t2.quantity * 100 ), '%' ) AS qoq
        FROM
        (
        SELECT
        CASE
        t4.sex
        WHEN '0' THEN
        '男'
        WHEN '1' THEN
        '女' ELSE '未知'
        END AS sex,
        count( 1 ) AS quantity
        FROM
        mst_order t1
        LEFT JOIN order_items t3 ON t3.order_id = t1.order_id
        LEFT JOIN sys_user t4 ON t1.user_id = t4.user_id
        LEFT JOIN store_user t5 ON t4.user_id = t5.user_id AND t5.store_id = t1.consumer_store_id
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND
        CASE
        #{type}
        WHEN 'day' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) = DATE_FORMAT( NOW( ), '%Y-%m-%d' )
        WHEN 'week' THEN
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' ) = DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL WEEKDAY( NOW( ) ) DAY ), '%Y-%m-%d' )
        WHEN 'month' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m' ) = DATE_FORMAT( NOW( ), '%Y-%m' ) ELSE 0
        END
        AND t3.product_id IS NOT NULL
        AND t3.product_id = #{productId}
        AND t1.consumer_store_id = #{storeId}
        AND t5.store_id = #{storeId}
        GROUP BY
        t4.sex
        ) t1
        LEFT JOIN (
        SELECT
        CASE
        t4.sex
        WHEN '0' THEN
        '男'
        WHEN '1' THEN
        '女' ELSE '未知'
        END AS sex,
        CASE
        #{type}
        WHEN 'day' THEN
        count( 1 )
        WHEN 'week' THEN
        ROUND( count( 1 ) / 7 * ( WEEKDAY ( NOW( ) ) + 1 ) )
        WHEN 'month' THEN
        ROUND(
        count( 1 ) / DAY ( LAST_DAY( CONCAT( DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 1 MONTH ), '%Y-%m' ), '-01' ) ) ) * DAY ( NOW( ) )
        ) ELSE 0
        END AS quantity
        FROM
        mst_order t1
        LEFT JOIN order_items t3 ON t3.order_id = t1.order_id
        LEFT JOIN sys_user t4 ON t1.user_id = t4.user_id
        LEFT JOIN store_user t5 ON t4.user_id = t5.user_id AND t5.store_id = t1.consumer_store_id
        WHERE
        t1.enabled = 1
        AND t1.order_status = '6'
        AND
        CASE
        #{type}
        WHEN 'day' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m-%d' ) = DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 1 DAY ), '%Y-%m-%d' )
        WHEN 'week' THEN
        DATE_FORMAT( DATE_SUB( t1.create_time, INTERVAL WEEKDAY( t1.create_time ) + 0 DAY ), '%Y-%m-%d' ) = DATE_FORMAT(
        DATE_SUB(
        DATE_SUB( NOW( ), INTERVAL 7 DAY ),
        INTERVAL WEEKDAY( DATE_SUB( NOW( ), INTERVAL 7 DAY ) ) + 0 DAY
        ),
        '%Y-%m-%d'
        )
        WHEN 'month' THEN
        DATE_FORMAT( t1.create_time, '%Y-%m' ) = DATE_FORMAT( DATE_SUB( NOW( ), INTERVAL 1 MONTH ), '%Y-%m' ) ELSE 0
        END
        AND t3.product_id IS NOT NULL
        AND t3.product_id = #{productId}
        AND t1.consumer_store_id = #{storeId}
        AND t5.store_id = #{storeId}
        GROUP BY
        t4.sex
        ) t2 ON t1.sex = t2.sex
    </select>

    <select id="shopInShopPersonalCount"
            resultType="java.util.Map"
            parameterType="jp.co.nttdata.sz.web.controller.report.dto.ReportAnalysisInputDto">
        SELECT
        count( 1 ) AS quantity
        FROM mst_shopcar AS t1
        LEFT JOIN sys_user AS t2 ON t1.follow_user = t2.user_id
        WHERE t1.act_status >=0
        AND DATE_FORMAT( t1.start_time, '%Y-%m-%d' ) = DATE_FORMAT( NOW( ), '%Y-%m-%d' )
        <if test="storeId !=null and storeId !=''">
            AND t1.store_id = #{storeId}
        </if>
        ORDER BY start_time DESC
    </select>
</mapper>
