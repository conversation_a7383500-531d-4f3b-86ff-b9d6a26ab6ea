<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.dao.MstShelfDao">

    <resultMap type="jp.co.nttdata.sz.web.entity.MstShelf" id="MstShelfResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="mac"    column="mac"    />
        <result property="isLocked"    column="is_locked"    />
        <result property="deviceTypeName"    column="device_type_name"    />
        <result property="address"    column="address"    />
        <result property="description"    column="description"    />
        <result property="hardwareCode"    column="hardware_code"    />
        <result property="shutdownTime"    column="shutdown_time"    />
        <result property="shelfDirectionAngle"    column="shelf_direction_angle"    />
        <result property="resolutionLength"    column="resolution_length"    />
        <result property="startPointX"    column="start_point_x"    />
        <result property="resolutionWidth"    column="resolution_width"    />
        <result property="startPointY"    column="start_point_y"    />
        <result property="resolutionHeight"    column="resolution_height"    />
        <result property="subKey"    column="sub_key"    />
        <result property="os"    column="os"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="organizationUnitName"    column="organization_unit_name"    />
        <result property="origanizationUnitId"    column="origanization_unit_id"    />
        <result property="outType"    column="out_type"    />
        <result property="footerHeight"    column="footer_height"    />
        <result property="outerPartitionHeight"    column="outer_partition_height"    />
        <result property="layerPartitionHeight"    column="layer_partition_height"    />
        <result property="childDevices"    column="child_devices"    />
        <result property="belongStore"    column="belong_store"    />
        <result property="config"    column="config"    />
        <collection property="mstShelfLayerList" notNullColumn="sub_layer_id" javaType="java.util.List" resultMap="MstShelfLayerResult" />
    </resultMap>

    <resultMap type="jp.co.nttdata.sz.web.entity.MstShelfLayer" id="MstShelfLayerResult">
        <result property="layerId"    column="sub_layer_id"    />
        <result property="shelfId"    column="sub_shelf_id"    />
        <result property="layerHeight"    column="sub_layer_height"    />
        <result property="startPointZ"    column="sub_start_point_z"    />
        <result property="index"    column="sub_index"    />
        <result property="cargoRoadCount"    column="sub_cargo_road_count"    />
        <collection property="shelfCargoRoadList" notNullColumn="third_id" javaType="java.util.List" resultMap="ShelfCargoRoadResult" />
    </resultMap>

    <resultMap type="jp.co.nttdata.sz.web.entity.ShelfCargoRoad" id="ShelfCargoRoadResult">
        <result property="id"    column="third_id"    />
        <result property="deviceId"    column="third_device_id"    />
        <result property="cargoType"    column="third_cargo_type"    />
        <result property="cargoRoadId"    column="third_cargo_road_id"    />
        <result property="startLength"    column="third_start_length"    />
        <result property="sensors"    column="third_sensors"    />
        <result property="warningWeight"    column="third_warning_weight"    />
        <result property="grossWeight"    column="gross_weight"    />
        <result property="name"    column="third_name"    />
        <result property="outerId"    column="third_outer_id"    />
        <result property="isEnabled"    column="third_is_enabled"    />
        <result property="stockCapacity"    column="third_stock_capacity"    />
        <result property="description"    column="third_description"    />
        <result property="extensionData"    column="third_extension_data"    />
        <result property="layerId"    column="third_layer_id"    />
        <result property="resolutionLength"    column="third_resolution_length"    />
        <result property="resolutionWidth"    column="third_resolution_width"    />
        <result property="startPointX"    column="third_start_point_x"    />
        <result property="resolutionHeight"    column="third_resolution_height"    />
        <result property="startPointZ"    column="third_start_point_z"    />
        <result property="spanRows"    column="third_span_rows"    />
        <result property="spanColums"    column="third_span_colums"    />
        <collection property="shelfCargoThings" notNullColumn="fouth_id" javaType="java.util.List" resultMap="ShelfCargoThingsResult" />
    </resultMap>

    <resultMap type="jp.co.nttdata.sz.web.entity.ShelfCargoThings" id="ShelfCargoThingsResult">
        <result property="id"    column="fouth_id"    />
        <result property="cargoThingId"    column="fouth_cargo_thing_id"    />
        <result property="cargoRoadId"    column="fouth_cargo_road_id"    />
        <result property="thingId"    column="fouth_thing_id"    />
        <result property="type"    column="fouth_type"    />
        <result property="thingName"    column="fouth_thing_name"    />
        <result property="stock"    column="fouth_stock"    />
        <result property="description"    column="fouth_description"    />
        <result property="extensionData"    column="fouth_extension_data"    />
        <result property="iconUrl"    column="fouth_icon_url"    />
        <result property="orderNumber"    column="fouth_order_number"    />
        <result property="thingOuterId"    column="fouth_thing_outer_id"    />
        <association property="product" javaType="jp.co.nttdata.sz.web.entity.ProductSkus">
            <result property="id"    column="fifth_id"    />
            <result property="skuId"    column="fifth_sku_id"    />
            <result property="quantity"    column="fifth_quantity"    />
            <result property="barcode"    column="fifth_barcode"    />
            <result property="propsName"    column="fifth_props_name"    />
            <result property="title"    column="fifth_title"    />
            <result property="price"    column="fifth_price"    />
            <result property="promPrice"    column="fifth_prom_pice"    />
            <result property="salesVolume"    column="fifth_sales_volum"    />
            <result property="likeCount"    column="fifth_like_count"    />
            <result property="keyWords"    column="fifth_key_words"    />
            <result property="picUrl"    column="fifth_pic_url"    />
            <result property="description"    column="fifth_description"    />
            <result property="outerId"    column="fifth_outer_id"    />
            <result property="orderNumber"    column="fifth_order_number"    />
            <result property="fromType"    column="fifth_from_type"    />
            <result property="colorName"    column="fifth_color_name"    />
            <result property="ageScope"    column="fifth_age_scope"    />
            <result property="gender"    column="fifth_gender"    />
            <result property="rfidCode"    column="fifth_rfid_code"    />
            <result property="price2"    column="fifth_price2"    />
            <result property="weight"    column="fifth_weight"    />
            <result property="pointRedeemType"    column="fifth_point_redeem_type"    />
            <result property="productId"    column="fifth_product_id"    />
            <collection property="productImageList" notNullColumn="sixth_product_id" javaType="java.util.List" resultMap="ProductImageResult" />
        </association>
    </resultMap>

    <resultMap type="jp.co.nttdata.sz.web.entity.ProductImage" id="ProductImageResult">
        <result property="propertyName"    column="sixth_property_name"    />
        <result property="propImgId"    column="sixth_prop_img_id"    />
        <result property="imageUrl"    column="sixth_image_url"    />
        <result property="productId"    column="sixth_product_id"    />
    </resultMap>

    <sql id="selectMstShelfVo">
        select id, name, mac, is_locked, device_type_name, address, description, hardware_code, shutdown_time, shelf_direction_angle, resolution_length, start_point_x, resolution_width, start_point_y, resolution_height, sub_key, os, tenant_name, tenant_id, organization_unit_name, origanization_unit_id, out_type, footer_height, outer_partition_height, layer_partition_height, child_devices, belong_store from mst_shelf
    </sql>

    <sql id="selectShelfAllVo">
        SELECT
        a.id,
        a.NAME,
        a.mac,
        a.is_locked,
        a.device_type_name,
        a.address,
        a.description,
        a.hardware_code,
        a.shutdown_time,
        a.shelf_direction_angle,
        a.resolution_length,
        a.start_point_x,
        a.resolution_width,
        a.start_point_y,
        a.resolution_height,
        a.sub_key,
        a.os,
        a.tenant_name,
        a.tenant_id,
        a.organization_unit_name,
        a.origanization_unit_id,
        a.out_type,
        a.footer_height,
        a.outer_partition_height,
        a.layer_partition_height,
        a.child_devices,
        a.belong_store,
        a.config,
        b.layer_id AS sub_layer_id,
        b.shelf_id AS sub_shelf_id,
        b.layer_height AS sub_layer_height,
        b.start_point_z AS sub_start_point_z,
        b.INDEX AS sub_index,
        b.cargo_road_count AS sub_cargo_road_count,
        c.id AS third_id,
        c.device_id AS third_device_id,
        c.cargo_type AS third_cargo_type,
        c.cargo_road_id AS third_cargo_road_id,
        c.start_length AS third_start_length,
        c.sensors AS third_sensors,
        se.warning_weight AS third_warning_weight,
        c.NAME AS third_name,
        c.outer_id AS third_outer_id,
        c.is_enabled AS third_is_enabled,
        c.stock_capacity AS third_stock_capacity,
        c.description AS third_description,
        c.extension_data AS third_extension_data,
        c.layer_id AS third_layer_id,
        c.resolution_length AS third_resolution_length,
        c.resolution_width AS third_resolution_width,
        c.start_point_x AS third_start_point_x,
        c.resolution_height AS third_resolution_height,
        c.start_point_z AS third_start_point_z,
        c.span_rows AS third_span_rows,
        c.gross_weight AS gross_weight,
        c.span_colums AS third_span_colums,
        d.id AS fouth_id,
        d.cargo_thing_id AS fouth_cargo_thing_id,
        d.cargo_road_id AS fouth_cargo_road_id,
        d.thing_id AS fouth_thing_id,
        d.type AS fouth_type,
        d.thing_name AS fouth_thing_name,
        d.stock AS fouth_stock,
        d.description AS fouth_description,
        d.extension_data AS fouth_extension_data,
        d.icon_url AS fouth_icon_url,
        d.order_number AS fouth_order_number,
        d.thing_outer_id AS fouth_thing_outer_id,
        e.id AS fifth_id,
        e.sku_id AS fifth_sku_id,
        e.quantity AS fifth_quantity,
        e.barcode AS fifth_barcode,
        e.props_name AS fifth_props_name,
        e.title AS fifth_title,
        e.price AS fifth_price,
        e.prom_pice AS fifth_prom_pice,
        e.sales_volum AS fifth_sales_volum,
        e.like_count AS fifth_like_count,
        e.key_words AS fifth_key_words,
        e.pic_url AS fifth_pic_url,
        e.description AS fifth_description,
        e.outer_id AS fifth_outer_id,
        e.order_number AS fifth_order_number,
        e.from_type AS fifth_from_type,
        e.color_name AS fifth_color_name,
        e.age_scope AS fifth_age_scope,
        e.gender AS fifth_gender,
        e.rfid_code AS fifth_rfid_code,
        e.price2 AS fifth_price2,
        e.weight AS fifth_weight,
        e.point_redeem_type AS fifth_point_redeem_type,
        e.product_id AS fifth_product_id,
        f.property_name AS sixth_property_name,
        f.prop_img_id AS sixth_prop_img_id,
        f.image_url AS sixth_image_url,
        f.product_id AS sixth_product_id
        FROM
        mst_shelf a
        LEFT JOIN mst_shelf_layer b ON b.shelf_id = a.id
        LEFT JOIN shelf_cargo_roads c ON c.layer_id = b.layer_id
        LEFT JOIN shelf_cargo_things d ON d.cargo_road_id = c.id
        LEFT JOIN product_skus e ON e.id = d.thing_id
        LEFT JOIN product_prop_imgs f ON f.product_id = e.product_id
        LEFT JOIN shelf_cargo_roads_ext se ON se.cargo_road_id = c.id
    </sql>

    <select id="selectMstShelfList" parameterType="jp.co.nttdata.sz.web.entity.MstShelf" resultMap="MstShelfResult">
        <include refid="selectShelfAllVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="isLocked != null "> and is_locked = #{isLocked}</if>
            <if test="deviceTypeName != null  and deviceTypeName != ''"> and device_type_name like concat('%', #{deviceTypeName}, '%')</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="hardwareCode != null  and hardwareCode != ''"> and hardware_code = #{hardwareCode}</if>
            <if test="shutdownTime != null "> and shutdown_time = #{shutdownTime}</if>
            <if test="shelfDirectionAngle != null "> and shelf_direction_angle = #{shelfDirectionAngle}</if>
            <if test="resolutionLength != null "> and resolution_length = #{resolutionLength}</if>
            <if test="startPointX != null "> and start_point_x = #{startPointX}</if>
            <if test="resolutionWidth != null "> and resolution_width = #{resolutionWidth}</if>
            <if test="startPointY != null "> and start_point_y = #{startPointY}</if>
            <if test="resolutionHeight != null "> and resolution_height = #{resolutionHeight}</if>
            <if test="subKey != null  and subKey != ''"> and sub_key = #{subKey}</if>
            <if test="os != null  and os != ''"> and os = #{os}</if>
            <if test="tenantName != null  and tenantName != ''"> and tenant_name like concat('%', #{tenantName}, '%')</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            <if test="organizationUnitName != null  and organizationUnitName != ''"> and organization_unit_name like concat('%', #{organizationUnitName}, '%')</if>
            <if test="origanizationUnitId != null "> and origanization_unit_id = #{origanizationUnitId}</if>
            <if test="outType != null  and outType != ''"> and out_type = #{outType}</if>
            <if test="footerHeight != null "> and footer_height = #{footerHeight}</if>
            <if test="outerPartitionHeight != null "> and outer_partition_height = #{outerPartitionHeight}</if>
            <if test="layerPartitionHeight != null "> and layer_partition_height = #{layerPartitionHeight}</if>
            <if test="childDevices != null  and childDevices != ''"> and child_devices = #{childDevices}</if>
            <if test="belongStore != null  and belongStore != ''"> and a.belong_store = #{belongStore}</if>
        </where>
    </select>

    <select id="selectMstShelfById" parameterType="Integer" resultMap="MstShelfResult">
        <include refid="selectShelfAllVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertMstShelf" parameterType="jp.co.nttdata.sz.web.entity.MstShelf" useGeneratedKeys="true" keyProperty="id">
        insert into mst_shelf
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="mac != null">mac,</if>
            <if test="isLocked != null">is_locked,</if>
            <if test="deviceTypeName != null">device_type_name,</if>
            <if test="address != null">address,</if>
            <if test="description != null">description,</if>
            <if test="hardwareCode != null">hardware_code,</if>
            <if test="shutdownTime != null">shutdown_time,</if>
            <if test="shelfDirectionAngle != null">shelf_direction_angle,</if>
            <if test="resolutionLength != null">resolution_length,</if>
            <if test="startPointX != null">start_point_x,</if>
            <if test="resolutionWidth != null">resolution_width,</if>
            <if test="startPointY != null">start_point_y,</if>
            <if test="resolutionHeight != null">resolution_height,</if>
            <if test="subKey != null">sub_key,</if>
            <if test="os != null">os,</if>
            <if test="tenantName != null">tenant_name,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="organizationUnitName != null">organization_unit_name,</if>
            <if test="origanizationUnitId != null">origanization_unit_id,</if>
            <if test="outType != null">out_type,</if>
            <if test="footerHeight != null">footer_height,</if>
            <if test="outerPartitionHeight != null">outer_partition_height,</if>
            <if test="layerPartitionHeight != null">layer_partition_height,</if>
            <if test="childDevices != null">child_devices,</if>
            <if test="belongStore != null and belongStore != ''">belong_store,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="mac != null">#{mac},</if>
            <if test="isLocked != null">#{isLocked},</if>
            <if test="deviceTypeName != null">#{deviceTypeName},</if>
            <if test="address != null">#{address},</if>
            <if test="description != null">#{description},</if>
            <if test="hardwareCode != null">#{hardwareCode},</if>
            <if test="shutdownTime != null">#{shutdownTime},</if>
            <if test="shelfDirectionAngle != null">#{shelfDirectionAngle},</if>
            <if test="resolutionLength != null">#{resolutionLength},</if>
            <if test="startPointX != null">#{startPointX},</if>
            <if test="resolutionWidth != null">#{resolutionWidth},</if>
            <if test="startPointY != null">#{startPointY},</if>
            <if test="resolutionHeight != null">#{resolutionHeight},</if>
            <if test="subKey != null">#{subKey},</if>
            <if test="os != null">#{os},</if>
            <if test="tenantName != null">#{tenantName},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="organizationUnitName != null">#{organizationUnitName},</if>
            <if test="origanizationUnitId != null">#{origanizationUnitId},</if>
            <if test="outType != null">#{outType},</if>
            <if test="footerHeight != null">#{footerHeight},</if>
            <if test="outerPartitionHeight != null">#{outerPartitionHeight},</if>
            <if test="layerPartitionHeight != null">#{layerPartitionHeight},</if>
            <if test="childDevices != null">#{childDevices},</if>
            <if test="belongStore != null and belongStore != ''">#{belongStore},</if>
         </trim>
    </insert>

    <update id="updateMstShelf" parameterType="jp.co.nttdata.sz.web.entity.MstShelf">
        update mst_shelf
        <trim prefix="SET" suffixOverrides=",">
            name = #{name},
            mac = #{mac},
            is_locked = #{isLocked},
            device_type_name = #{deviceTypeName},
            address = #{address},
            description = #{description},
            hardware_code = #{hardwareCode},
            shutdown_time = #{shutdownTime},
            shelf_direction_angle = #{shelfDirectionAngle},
            resolution_length = #{resolutionLength},
            start_point_x = #{startPointX},
            resolution_width = #{resolutionWidth},
            start_point_y = #{startPointY},
            resolution_height = #{resolutionHeight},
            sub_key = #{subKey},
            os = #{os},
            tenant_name = #{tenantName},
            tenant_id = #{tenantId},
            organization_unit_name = #{organizationUnitName},
            origanization_unit_id = #{origanizationUnitId},
            out_type = #{outType},
            footer_height = #{footerHeight},
            outer_partition_height = #{outerPartitionHeight},
            layer_partition_height = #{layerPartitionHeight},
            child_devices = #{childDevices},
            belong_store = #{belongStore},
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMstShelfById" parameterType="Integer">
        delete from mst_shelf where id = #{id}
    </delete>

    <delete id="deleteMstShelfByIds" parameterType="String">
        delete from mst_shelf where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteMstShelfLayerByShelfIds" parameterType="String">
        delete from mst_shelf_layer where shelf_id in
        <foreach item="shelfId" collection="array" open="(" separator="," close=")">
            #{shelfId}
        </foreach>
    </delete>

    <delete id="deleteMstShelfLayerByShelfId" parameterType="Long">
        delete from mst_shelf_layer where shelf_id = #{shelfId}
    </delete>

    <insert id="batchMstShelfLayer">
        insert into mst_shelf_layer( layer_id, shelf_id, layer_height, start_point_z, index, cargo_road_count) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.layerId}, #{item.shelfId}, #{item.layerHeight}, #{item.startPointZ}, #{item.index}, #{item.cargoRoadCount})
        </foreach>
    </insert>
</mapper>
