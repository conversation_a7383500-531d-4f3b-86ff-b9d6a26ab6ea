<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.dao.ProductPropImgsMapper">

    <resultMap type="jp.co.nttdata.sz.web.service.product.dto.ProductPropImgs" id="ProductPropImgsResult">
        <result property="propertyName"    column="property_name"    />
        <result property="propImgId"    column="prop_img_id"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="productId"    column="product_id"    />
        <result property="isUpdated"    column="is_updated"    />
        <result property="belongStore"    column="belong_store"    />
    </resultMap>

    <sql id="selectProductPropImgsVo">
        select property_name, prop_img_id, image_url, product_id, is_updated, belong_store from product_prop_imgs
    </sql>

    <select id="selectProductPropImgsList" parameterType="jp.co.nttdata.sz.web.service.product.dto.ProductPropImgs" resultMap="ProductPropImgsResult">
        <include refid="selectProductPropImgsVo"/>
        <where>
            <if test="propertyName != null  and propertyName != ''"> and property_name like concat('%', #{propertyName}, '%')</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="isUpdated != null "> and is_updated = #{isUpdated}</if>
            <if test="belongStore != null  and belongStore != ''"> and belong_store = #{belongStore}</if>
        </where>
    </select>

    <select id="selectProductPropImgsByPropImgId" resultMap="ProductPropImgsResult">
        <include refid="selectProductPropImgsVo"/>
        where prop_img_id = #{propImgId}
        and belong_store = #{storeId}
    </select>

    <insert id="insertProductPropImgs" parameterType="jp.co.nttdata.sz.web.service.product.dto.ProductPropImgs" useGeneratedKeys="true" keyProperty="propImgId">
        insert into product_prop_imgs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="propertyName != null">property_name,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="productId != null">product_id,</if>
            <if test="isUpdated != null">is_updated,</if>
            <if test="belongStore != null">belong_store,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="propertyName != null">#{propertyName},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="productId != null">#{productId},</if>
            <if test="isUpdated != null">#{isUpdated},</if>
            <if test="belongStore != null">#{belongStore},</if>
         </trim>
    </insert>

    <update id="updateProductPropImgs" parameterType="jp.co.nttdata.sz.web.service.product.dto.ProductPropImgs">
        update product_prop_imgs
        <trim prefix="SET" suffixOverrides=",">
            <if test="propertyName != null">property_name = #{propertyName},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="isUpdated != null">is_updated = #{isUpdated},</if>
        </trim>
        where prop_img_id = #{propImgId}  and belong_store  = #{belongStore}
    </update>

    <delete id="deleteProductPropImgsByPropImgId" parameterType="Long">
        delete from product_prop_imgs where prop_img_id = #{propImgId}
    </delete>

    <delete id="deleteProductPropImgsByPropImgIds" parameterType="String">
        delete from product_prop_imgs where prop_img_id in
        <foreach item="propImgId" collection="array" open="(" separator="," close=")">
            #{propImgId}
        </foreach>
       and belong_store = #{storeId}
    </delete>


    <insert id="importProductPropImgs">
        insert into product_prop_imgs
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="propertyName != null">property_name,</if>
                <if test="propImgId != null">prop_img_id,</if>
                <if test="imageUrl != null">image_url,</if>
                <if test="productId != null">product_id,</if>
                <if test="isUpdated != null">is_updated,</if>
                <if test="belongStore != null">belong_store,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="propertyName != null">#{propertyName},</if>
                <if test="propImgId != null">#{propImgId},</if>
                <if test="imageUrl != null">#{imageUrl},</if>
                <if test="productId != null">#{productId},</if>
                <if test="isUpdated != null">#{isUpdated},</if>
                <if test="belongStore != null">#{belongStore},</if>
        </trim>
    </insert>

    <update id="cleanProductPropImgs">
        truncate table product_prop_imgs
    </update>
</mapper>
