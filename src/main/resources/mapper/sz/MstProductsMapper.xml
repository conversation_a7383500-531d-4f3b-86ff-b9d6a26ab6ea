<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.dao.MstProductsMapper">

    <resultMap type="jp.co.nttdata.sz.web.service.product.dto.MstProducts" id="MstProductsResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="quantity"    column="quantity"    />
        <result property="title"    column="title"    />
        <result property="organizationUnitId"    column="organization_unit_id"    />
        <result property="subTitle"    column="sub_title"    />
        <result property="price"    column="price"    />
        <result property="promPrice"    column="prom_price"    />
        <result property="barcode"    column="barcode"    />
        <result property="salesVolume"    column="sales_volume"    />
        <result property="keywords"    column="keywords"    />
        <result property="picUrl"    column="pic_url"    />
        <result property="likeCount"    column="like_count"    />
        <result property="description"    column="description"    />
        <result property="isFromBrand"    column="is_from_brand"    />
        <result property="sellerId"    column="seller_id"    />
        <result property="outerId"    column="outer_id"    />
        <result property="fromType"    column="from_type"    />
        <result property="hasRealSkus"    column="has_real_skus"    />
        <result property="ageScope"    column="age_scope"    />
        <result property="gender"    column="gender"    />
        <result property="rfidCode"    column="rfid_code"    />
        <result property="price2"    column="price2"    />
        <result property="groupQrCodeInfo"    column="group_qr_code_info"    />
        <result property="orderNumber"    column="order_number"    />
        <result property="brandId"    column="brand_id"    />
        <result property="language"    column="language"    />
        <result property="region"    column="region"    />
        <result property="pointReadeemType"    column="point_readeem_type"    />
        <result property="isUpdated"    column="is_updated"    />
        <result property="isOpen"    column="is_open"    />
        <result property="belongStore"    column="belong_store"    />
    </resultMap>

    <sql id="selectMstProductsVo">
        select id, item_id, quantity, title, organization_unit_id, sub_title, price, prom_price, barcode, sales_volume, keywords, pic_url, like_count, description, is_from_brand, seller_id, outer_id, from_type, has_real_skus, age_scope, gender, rfid_code, price2, group_qr_code_info, order_number, brand_id, language, region, point_readeem_type, is_updated, is_open, belong_store from mst_products
    </sql>

    <select id="selectMstProductsList" parameterType="jp.co.nttdata.sz.web.service.product.dto.MstProducts"
            resultMap="MstProductsResult">
        <include refid="selectMstProductsVo"/>
        <where>
            <if test="itemId != null  and itemId != ''"> and item_id = #{itemId}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="title != null  and title != ''"> and title like concat('%',#{title},'%')</if>
            <if test="organizationUnitId != null "> and organization_unit_id = #{organizationUnitId}</if>
            <if test="subTitle != null  and subTitle != ''"> and sub_title  like concat('%',#{subTitle},'%')</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="promPrice != null "> and prom_price = #{promPrice}</if>
            <if test="barcode != null  and barcode != ''"> and barcode = #{barcode}</if>
            <if test="salesVolume != null "> and sales_volume = #{salesVolume}</if>
            <if test="keywords != null  and keywords != ''"> and keywords like concat('%',#{keywords},'%')</if>
            <if test="picUrl != null  and picUrl != ''"> and pic_url = #{picUrl}</if>
            <if test="likeCount != null "> and like_count = #{likeCount}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="isFromBrand != null "> and is_from_brand = #{isFromBrand}</if>
            <if test="sellerId != null  and sellerId != ''"> and seller_id = #{sellerId}</if>
            <if test="outerId != null  and outerId != ''"> and outer_id = #{outerId}</if>
            <if test="fromType != null  and fromType != ''"> and from_type = #{fromType}</if>
            <if test="hasRealSkus != null "> and has_real_skus = #{hasRealSkus}</if>
            <if test="ageScope != null  and ageScope != ''"> and age_scope = #{ageScope}</if>
            <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
            <if test="rfidCode != null  and rfidCode != ''"> and rfid_code  like concat('%',#{rfidCode},'%')</if>
            <if test="price2 != null  and price2 != ''"> and price2 = #{price2}</if>
            <if test="groupQrCodeInfo != null  and groupQrCodeInfo != ''"> and group_qr_code_info = #{groupQrCodeInfo}</if>
            <if test="orderNumber != null "> and order_number = #{orderNumber}</if>
            <if test="brandId != null "> and brand_id = #{brandId}</if>
            <if test="language != null  and language != ''"> and language = #{language}</if>
            <if test="region != null  and region != ''"> and region = #{region}</if>
            <if test="pointReadeemType != null  and pointReadeemType != ''"> and point_readeem_type = #{pointReadeemType}</if>
            <if test="isUpdated != null "> and is_updated = #{isUpdated}</if>
            <if test="isOpen != null "> and is_open = #{isOpen}</if>
            <if test="belongStore != null "> and belong_store = #{belongStore}</if>
        </where>
    </select>

    <select id="selectMstProductsById" resultMap="MstProductsResult">
        <include refid="selectMstProductsVo"/>
        where id = #{id} and belong_store  = #{storeId}
    </select>
    <select id="getMaxId" resultType="java.lang.Long">
        select COALESCE(max(id)+1, 1) from mst_products where belong_store =#{storeId}
    </select>

    <insert id="insertMstProducts" parameterType="jp.co.nttdata.sz.web.service.product.dto.MstProducts">
        insert into mst_products
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="itemId != null and itemId != ''">item_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="title != null">title,</if>
            <if test="organizationUnitId != null">organization_unit_id,</if>
            <if test="subTitle != null">sub_title,</if>
            <if test="price != null">price,</if>
            <if test="promPrice != null">prom_price,</if>
            <if test="barcode != null">barcode,</if>
            <if test="salesVolume != null">sales_volume,</if>
            <if test="keywords != null">keywords,</if>
            <if test="picUrl != null">pic_url,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="description != null">description,</if>
            <if test="isFromBrand != null">is_from_brand,</if>
            <if test="sellerId != null">seller_id,</if>
            <if test="outerId != null">outer_id,</if>
            <if test="fromType != null">from_type,</if>
            <if test="hasRealSkus != null">has_real_skus,</if>
            <if test="ageScope != null">age_scope,</if>
            <if test="gender != null">gender,</if>
            <if test="rfidCode != null">rfid_code,</if>
            <if test="price2 != null">price2,</if>
            <if test="groupQrCodeInfo != null">group_qr_code_info,</if>
            <if test="orderNumber != null">order_number,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="language != null">language,</if>
            <if test="region != null">region,</if>
            <if test="pointReadeemType != null">point_readeem_type,</if>
            <if test="isUpdated != null">is_updated,</if>
            <if test="isOpen != null">is_open,</if>
            <if test="belongStore != null">belong_store,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="itemId != null and itemId != ''">#{itemId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="title != null">#{title},</if>
            <if test="organizationUnitId != null">#{organizationUnitId},</if>
            <if test="subTitle != null">#{subTitle},</if>
            <if test="price != null">#{price},</if>
            <if test="promPrice != null">#{promPrice},</if>
            <if test="barcode != null">#{barcode},</if>
            <if test="salesVolume != null">#{salesVolume},</if>
            <if test="keywords != null">#{keywords},</if>
            <if test="picUrl != null">#{picUrl},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="description != null">#{description},</if>
            <if test="isFromBrand != null">#{isFromBrand},</if>
            <if test="sellerId != null">#{sellerId},</if>
            <if test="outerId != null">#{outerId},</if>
            <if test="fromType != null">#{fromType},</if>
            <if test="hasRealSkus != null">#{hasRealSkus},</if>
            <if test="ageScope != null">#{ageScope},</if>
            <if test="gender != null">#{gender},</if>
            <if test="rfidCode != null">#{rfidCode},</if>
            <if test="price2 != null">#{price2},</if>
            <if test="groupQrCodeInfo != null">#{groupQrCodeInfo},</if>
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="language != null">#{language},</if>
            <if test="region != null">#{region},</if>
            <if test="pointReadeemType != null">#{pointReadeemType},</if>
            <if test="isUpdated != null">#{isUpdated},</if>
            <if test="isOpen != null">#{isOpen},</if>
            <if test="belongStore != null">#{belongStore},</if>
         </trim>
    </insert>

    <update id="updateMstProducts" parameterType="jp.co.nttdata.sz.web.service.product.dto.MstProducts">
        update mst_products
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null and itemId != ''">item_id = #{itemId},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="title != null">title = #{title},</if>
            <if test="organizationUnitId != null">organization_unit_id = #{organizationUnitId},</if>
            <if test="subTitle != null">sub_title = #{subTitle},</if>
            <if test="price != null">price = #{price},</if>
            <if test="promPrice != null">prom_price = #{promPrice},</if>
            <if test="barcode != null">barcode = #{barcode},</if>
            <if test="salesVolume != null">sales_volume = #{salesVolume},</if>
            <if test="keywords != null">keywords = #{keywords},</if>
            <if test="picUrl != null">pic_url = #{picUrl},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="description != null">description = #{description},</if>
            <if test="isFromBrand != null">is_from_brand = #{isFromBrand},</if>
            <if test="sellerId != null">seller_id = #{sellerId},</if>
            <if test="outerId != null">outer_id = #{outerId},</if>
            <if test="fromType != null">from_type = #{fromType},</if>
            <if test="hasRealSkus != null">has_real_skus = #{hasRealSkus},</if>
            <if test="ageScope != null">age_scope = #{ageScope},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="rfidCode != null">rfid_code = #{rfidCode},</if>
            <if test="price2 != null">price2 = #{price2},</if>
            <if test="groupQrCodeInfo != null">group_qr_code_info = #{groupQrCodeInfo},</if>
            <if test="orderNumber != null">order_number = #{orderNumber},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="language != null">language = #{language},</if>
            <if test="region != null">region = #{region},</if>
            <if test="pointReadeemType != null">point_readeem_type = #{pointReadeemType},</if>
            <if test="isUpdated != null">is_updated = #{isUpdated},</if>

        </trim>
        where id = #{id}  and belong_store  = #{belongStore}
    </update>

    <delete id="deleteMstProductsById" parameterType="Long">
        delete from mst_products where id = #{id}  and belong_store  = #{storeId}
    </delete>

    <delete id="deleteMstProductsByIds" parameterType="String">
        delete from mst_products where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>   and belong_store  = #{storeId}
    </delete>


    <insert id="importMstProducts">
        insert into mst_products
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="itemId != null and itemId != ''">item_id,</if>
                <if test="quantity != null">quantity,</if>
                <if test="title != null">title,</if>
                <if test="organizationUnitId != null">organization_unit_id,</if>
                <if test="subTitle != null">sub_title,</if>
                <if test="price != null">price,</if>
                <if test="promPrice != null">prom_price,</if>
                <if test="barcode != null">barcode,</if>
                <if test="salesVolume != null">sales_volume,</if>
                <if test="keywords != null">keywords,</if>
                <if test="picUrl != null">pic_url,</if>
                <if test="likeCount != null">like_count,</if>
                <if test="description != null">description,</if>
                <if test="isFromBrand != null">is_from_brand,</if>
                <if test="sellerId != null">seller_id,</if>
                <if test="outerId != null">outer_id,</if>
                <if test="fromType != null">from_type,</if>
                <if test="hasRealSkus != null">has_real_skus,</if>
                <if test="ageScope != null">age_scope,</if>
                <if test="gender != null">gender,</if>
                <if test="rfidCode != null">rfid_code,</if>
                <if test="price2 != null">price2,</if>
                <if test="groupQrCodeInfo != null">group_qr_code_info,</if>
                <if test="orderNumber != null">order_number,</if>
                <if test="brandId != null">brand_id,</if>
                <if test="language != null">language,</if>
                <if test="region != null">region,</if>
                <if test="pointReadeemType != null">point_readeem_type,</if>
                            <if test="isUpdated != null">is_updated,</if>
            <if test="isOpen != null">is_open,</if>
            <if test="belongStore != null">belong_store,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="itemId != null and itemId != ''">#{itemId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="title != null">#{title},</if>
            <if test="organizationUnitId != null">#{organizationUnitId},</if>
            <if test="subTitle != null">#{subTitle},</if>
            <if test="price != null">#{price},</if>
            <if test="promPrice != null">#{promPrice},</if>
            <if test="barcode != null">#{barcode},</if>
            <if test="salesVolume != null">#{salesVolume},</if>
            <if test="keywords != null">#{keywords},</if>
            <if test="picUrl != null">#{picUrl},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="description != null">#{description},</if>
            <if test="isFromBrand != null">#{isFromBrand},</if>
            <if test="sellerId != null">#{sellerId},</if>
            <if test="outerId != null">#{outerId},</if>
            <if test="fromType != null">#{fromType},</if>
            <if test="hasRealSkus != null">#{hasRealSkus},</if>
            <if test="ageScope != null">#{ageScope},</if>
            <if test="gender != null">#{gender},</if>
            <if test="rfidCode != null">#{rfidCode},</if>
            <if test="price2 != null">#{price2},</if>
            <if test="groupQrCodeInfo != null">#{groupQrCodeInfo},</if>
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="language != null">#{language},</if>
            <if test="region != null">#{region},</if>
            <if test="pointReadeemType != null">#{pointReadeemType},</if>
            <if test="isUpdated != null">#{isUpdated},</if>
            <if test="isOpen != null">#{isOpen},</if>
            <if test="belongStore != null">#{belongStore},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="cleanMstProducts">
        truncate table mst_products
    </update>
</mapper>
