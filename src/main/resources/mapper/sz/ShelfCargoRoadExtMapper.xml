<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.nttdata.sz.web.dao.ShelfCargoRoadExtDao">

    <resultMap type="jp.co.nttdata.sz.web.entity.ShelfCargoRoad" id="ShelfCargoRoadExtResult">
        <result property="deviceId"    column="device_id"    />
        <result property="cargoRoadId"    column="cargo_road_id"  />
        <result property="warningWeight" column="warning_weight"/>
    </resultMap>

    <update id="updateShelfCargoRoadExt" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoad">
        UPDATE shelf_cargo_roads_ext
        SET warning_weight =#{warningWeight}
        WHERE
        cargo_road_id = #{id}
    </update>
    <insert id="insertSelfCargoRoadExt" parameterType="jp.co.nttdata.sz.web.entity.ShelfCargoRoad">
        INSERT INTO shelf_cargo_roads_ext(device_id,cargo_road_id,warning_weight) VALUES(#{deviceId},#{id},#{warningWeight})
    </insert>
    <select id="selectSelfCargoRoadExt" parameterType="Integer" resultMap="ShelfCargoRoadExtResult">
        select * from shelf_cargo_roads_ext where cargo_road_id = #{cargoRoadId}
    </select>
</mapper>