<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thanos.platform.fw.auth.dao.SysGroupDao">

	<!-- mapping -->
	<resultMap id="SysGroup" type="org.thanos.platform.fw.auth.damain.SysGroup">
		<result property="groupId" column="group_id" jdbcType="NUMERIC" />
		<result property="groupName" column="group_name" jdbcType="VARCHAR" />
		<result property="groupDesc" column="group_desc" jdbcType="VARCHAR" />
		<result property="enabled" column="enabled" jdbcType="NUMERIC" />
	</resultMap>

	<sql id="columns">
		group_id,
		group_name,
		group_desc,
		enabled
	</sql>

	<sql id="columns2">
		group_name,
		group_desc
	</sql>

	<!-- 查询组所有信息 -->
	<select id="getSysGroupAll" resultMap="SysGroup">
		SELECT DISTINCT
		<include refid="columns" />
		FROM
		sys_group
		ORDER BY
		group_id asc
	</select>

	<!-- 查询组所有信息 （可用only） -->
	<select id="getSysGroupAll2" resultMap="SysGroup">
		SELECT DISTINCT
		<include refid="columns" />
		FROM
		sys_group
		WHERE
		enabled = '1'
	</select>

	<!-- 追加用户组 -->
	<insert id="addSysGroup" parameterType="org.thanos.platform.fw.auth.damain.SysGroup">
		INSERT INTO
		sys_group
		(
		<include refid="columns2" />
		)
		VALUES
		(
		#{groupName,jdbcType=NUMERIC},
		#{groupDesc,jdbcType=VARCHAR}
		)
	</insert>

	<!-- 根据用户组ID查看用户組信息 -->
	<select id="getSysGroupByGroupId" resultMap="SysGroup">
		SELECT DISTINCT
		<include refid="columns" />
		FROM
		sys_group
		WHERE
		group_id = #{groupId}
	</select>

	<!-- 根据用户组ID编辑用户组信息 -->
	<update id="updSysGroupByGroupId" parameterType="org.thanos.platform.fw.auth.damain.SysGroup">
		UPDATE
		sys_group
		SET
		group_name=#{groupName,jdbcType=VARCHAR},
		enabled=#{enabled,jdbcType=NUMERIC},
		group_desc=#{groupDesc,jdbcType=VARCHAR}
		WHERE
		group_id = #{groupId}
	</update>

	<!-- 根据用户组ID 删除用户组（物理删除）. -->
	<delete id="delSysGroupByGroupId1">
		DELETE FROM sys_group
		WHERE
		group_id = #{groupId}
	</delete>

	<!-- 根据用户组ID 删除用户组（伦理删除：禁用） -->
	<update id="delSysGroupByGroupId2">
		UPDATE
		sys_group
		SET
		enabled = '0'
		WHERE
		group_id =
		#{groupId}
	</update>

</mapper>